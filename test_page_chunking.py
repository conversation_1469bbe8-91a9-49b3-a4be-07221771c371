#!/usr/bin/env python3
"""
Test script for the new page-based chunking strategy
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from document_processor import DocumentProcessor
from vector_store import VectorStore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_page_chunking():
    """Test the new page-based chunking functionality"""
    
    print("🧪 Testing Page-Based Chunking Strategy")
    print("=" * 50)
    
    # Initialize processors
    print("📝 Initializing document processor with page-based chunking...")
    doc_processor = DocumentProcessor(use_page_chunks=True)
    
    print("🗄️ Initializing vector store...")
    vector_store = VectorStore()
    
    # Test with a sample PDF
    test_pdf_path = "sample/onlyspares.pdf"  # Use existing sample PDF
    
    if not Path(test_pdf_path).exists():
        print(f"⚠️ Test PDF not found at {test_pdf_path}")
        print("Please add a test PDF file to test the functionality")
        return
    
    try:
        # Process document
        print(f"📄 Processing document: {test_pdf_path}")
        result = await doc_processor.process_pdf(test_pdf_path)
        
        if result.get("processing_status") == "success":
            print("✅ Document processed successfully!")
            
            # Print document metadata
            metadata = result.get("metadata", {})
            print(f"📊 Document Info:")
            print(f"   - Filename: {metadata.get('filename', 'N/A')}")
            print(f"   - Pages: {metadata.get('num_pages', 0)}")
            print(f"   - Title: {metadata.get('title', 'N/A')}")
            
            # Print pages info
            pages = result.get("pages", [])
            print(f"📄 Pages processed: {len(pages)}")
            
            for i, page in enumerate(pages[:3]):  # Show first 3 pages
                print(f"   Page {page.get('page_number', i+1)}:")
                print(f"     - Word count: {page.get('metadata', {}).get('word_count', 0)}")
                print(f"     - Has tables: {page.get('metadata', {}).get('has_tables', False)}")
                print(f"     - Table count: {page.get('metadata', {}).get('table_count', 0)}")
                print(f"     - Has images: {page.get('metadata', {}).get('has_images', False)}")
                print(f"     - First 100 chars: {page.get('metadata', {}).get('first_100_chars', '')[:50]}...")
            
            # Print chunks info
            chunks = result.get("chunks", [])
            print(f"🧩 Chunks created: {len(chunks)}")
            
            for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
                print(f"   Chunk {i+1}:")
                print(f"     - Type: {chunk.get('chunk_type', 'unknown')}")
                print(f"     - Page: {chunk.get('page_number', 'N/A')}")
                print(f"     - Word count: {chunk.get('word_count', 0)}")
                print(f"     - Has tables: {chunk.get('has_tables', False)}")
                print(f"     - First 100 chars: {chunk.get('first_100_chars', '')[:50]}...")
            
            # Test vector storage
            print("\n🗄️ Testing vector storage...")
            doc_id = await vector_store.store_document(result)
            print(f"✅ Document stored with ID: {doc_id}")
            
            # Test search functionality
            print("\n🔍 Testing search functionality...")
            
            # Test regular search
            search_results = await vector_store.similarity_search("test query", k=3)
            print(f"📊 Regular search results: {len(search_results)}")
            
            # Test table search
            table_results = await vector_store.search_tables("table data", k=3)
            print(f"📋 Table search results: {len(table_results)}")
            
            print("\n✅ All tests completed successfully!")
            
        else:
            print(f"❌ Document processing failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        logger.exception("Test error details:")

def test_chunk_creation():
    """Test chunk creation without PDF processing"""
    print("\n🧪 Testing Chunk Creation Logic")
    print("=" * 40)
    
    # Create mock page data
    mock_pages = [
        {
            "page_number": 1,
            "markdown": "This is the first page content with some text about procedures and safety.",
            "tables_info": [],
            "metadata": {
                "word_count": 12,
                "has_tables": False,
                "table_count": 0,
                "has_images": False,
                "image_count": 0,
                "first_100_chars": "This is the first page content with some text about procedures and safety."
            }
        },
        {
            "page_number": 2,
            "markdown": "This is the second page with a table showing data:\n\n| Column 1 | Column 2 |\n|----------|----------|\n| Data 1   | Data 2   |",
            "tables_info": [{"table_index": 0, "page_number": 2, "estimated_rows": 2, "estimated_cols": 2}],
            "metadata": {
                "word_count": 20,
                "has_tables": True,
                "table_count": 1,
                "has_images": False,
                "image_count": 0,
                "first_100_chars": "This is the second page with a table showing data:"
            }
        }
    ]
    
    mock_doc_metadata = {
        "filename": "test.pdf",
        "title": "Test Document"
    }
    
    # Test chunk creation
    doc_processor = DocumentProcessor(use_page_chunks=True)
    chunks = doc_processor._create_page_chunks(mock_pages, mock_doc_metadata)
    
    print(f"📊 Created {len(chunks)} chunks from {len(mock_pages)} pages")
    
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1}:")
        print(f"  - Page: {chunk.get('page_number')}")
        print(f"  - Type: {chunk.get('chunk_type')}")
        print(f"  - Word count: {chunk.get('word_count')}")
        print(f"  - Has tables: {chunk.get('has_tables')}")
        print(f"  - Table count: {chunk.get('table_count')}")
        print(f"  - Content preview: {chunk.get('text', '')[:100]}...")
    
    print("\n✅ Chunk creation test completed!")

if __name__ == "__main__":
    print("🚀 Starting Page-Based Chunking Tests")
    
    # Test chunk creation logic first
    test_chunk_creation()
    
    # Test full pipeline if PDF is available
    asyncio.run(test_page_chunking())
    
    print("\n🎉 All tests completed!")
