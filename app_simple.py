"""
Simple RAG Application using LangChain and LlamaIndex
Optimized Gradio interface for better performance
"""

import os
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
import gradio as gr
from pathlib import Path
import json
import pandas as pd
import threading
import time

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import our advanced processor
from src.langchain_processor import AdvancedRAGProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleRAGApp:
    """Simple RAG Application with optimized Gradio Interface"""
    
    def __init__(self):
        """Initialize the application"""
        self.processor = AdvancedRAGProcessor()
        self.processed_files = []
        self.conversation_history = []
        self.processing_status = "Ready"
        
        # Check environment variables
        self._check_environment()
        
        logger.info("Simple RAG App initialized")
    
    def _check_environment(self):
        """Check if all required environment variables are set"""
        required_vars = [
            "SUPABASE_URL",
            "SUPABASE_KEY", 
            "OPENAI_API_KEY"
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("Environment variables validated")
    
    def process_files_sync(self, files: List[str], provider: str = "openai") -> Tuple[str, str]:
        """Process uploaded files synchronously"""
        try:
            if not files:
                return "❌ No files provided", ""
            
            # Filter PDF files
            pdf_files = [f for f in files if f.lower().endswith('.pdf')]
            
            if not pdf_files:
                return "❌ No PDF files found", ""
            
            self.processing_status = f"Processing {len(pdf_files)} PDF files..."
            logger.info(f"Processing {len(pdf_files)} PDF files")
            
            # Process files using asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                results = loop.run_until_complete(self.processor.process_multiple_pdfs(pdf_files))
            finally:
                loop.close()
            
            # Setup chains
            self.processor.setup_chains(provider)
            
            # Update processed files list
            self.processed_files.extend(pdf_files)
            
            # Create status message
            status_msg = f"""
✅ **Processing Complete!**

📊 **Summary:**
- Total files processed: {len(results['processed'])}
- Total documents extracted: {results['total_documents']}
- Failed files: {len(results['failed'])}

📁 **Processed Files:**
{chr(10).join([f"• {Path(item['file']).name} ({item['documents_count']} chunks)" for item in results['processed']])}

{f"❌ **Failed Files:**{chr(10)}{chr(10).join([f'• {Path(item['file']).name}: {item['error']}' for item in results['failed']])}" if results['failed'] else ""}

🤖 **LLM Provider:** {provider.upper()}
🔍 **Ready for queries!**
"""
            
            # Create file list for display
            file_list = "\n".join([f"📄 {Path(f).name}" for f in self.processed_files])
            
            self.processing_status = "Ready"
            return status_msg, file_list
            
        except Exception as e:
            error_msg = f"❌ Error processing files: {str(e)}"
            logger.error(error_msg)
            self.processing_status = "Error"
            return error_msg, ""
    
    def query_documents_sync(self, query: str, method: str = "langchain") -> Tuple[str, str]:
        """Query processed documents synchronously"""
        try:
            logger.info(f"🔍 Starting query: '{query}' with method: {method}")

            if not query.strip():
                logger.warning("❌ Empty query provided")
                return "❌ Please enter a query", ""

            if not self.processed_files:
                logger.warning("❌ No processed files found")
                return "❌ No documents processed. Please upload and process documents first.", ""

            logger.info(f"📚 Processed files count: {len(self.processed_files)}")
            logger.info(f"🔧 Processor status: {self.processor is not None}")

            # Execute query based on method using asyncio
            logger.info("🔄 Creating new event loop for async query...")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                if method == "langchain":
                    logger.info("🔗 Executing LangChain query...")
                    result = loop.run_until_complete(self.processor.query_documents(query, use_conversation=False))
                elif method == "llama_index":
                    logger.info("🦙 Executing LlamaIndex query...")
                    result = loop.run_until_complete(self.processor.query_with_llama_index(query))
                elif method == "hybrid":
                    logger.info("🔀 Executing Hybrid query...")
                    result = loop.run_until_complete(self.processor.hybrid_query(query))
                else:
                    logger.error(f"❌ Invalid query method: {method}")
                    return "❌ Invalid query method", ""
            finally:
                loop.close()
                logger.info("🔄 Event loop closed")

            logger.info(f"📝 Query result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            logger.info(f"❌ Has error: {result.get('error', False)}")

            # Format response
            if result.get("error"):
                error_msg = f"❌ {result.get('answer', 'Unknown error')}"
                logger.error(error_msg)
                return error_msg, ""

            # Format answer
            if method == "hybrid":
                answer = result.get("combined_answer", "")
            else:
                answer = result.get("answer", "")

            logger.info(f"📄 Answer length: {len(answer)}")

            # Format sources
            sources_info = self._format_sources(result, method)
            logger.info(f"📚 Sources info length: {len(sources_info)}")

            # Update conversation history
            self.conversation_history.append({
                "query": query,
                "answer": answer,
                "method": method,
                "timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
            })

            logger.info("✅ Query completed successfully")
            return answer, sources_info

        except Exception as e:
            error_msg = f"❌ Error querying documents: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return error_msg, ""
    
    def _format_sources(self, result: Dict[str, Any], method: str) -> str:
        """Format source information"""
        try:
            if method == "langchain":
                sources = result.get("source_documents", [])
                if not sources:
                    return "No sources found"
                
                formatted_sources = []
                for i, source in enumerate(sources[:3], 1):  # Limit to top 3
                    formatted_sources.append(f"""
**Source {i}:**
- File: {source.get('metadata', {}).get('filename', 'Unknown')}
- Page: {source.get('page', 'Unknown')}
- Content: {source.get('content', '')[:150]}...
""")
                return "\n".join(formatted_sources)
            
            elif method == "llama_index":
                sources = result.get("source_nodes", [])
                if not sources:
                    return "No sources found"
                
                formatted_sources = []
                for i, source in enumerate(sources[:3], 1):
                    formatted_sources.append(f"""
**Source {i}:** (Score: {source.get('score', 0):.3f})
- Content: {source.get('content', '')[:150]}...
""")
                return "\n".join(formatted_sources)
            
            elif method == "hybrid":
                return "Hybrid sources from both LangChain and LlamaIndex"
            
            return "No sources available"
            
        except Exception as e:
            return f"Error formatting sources: {str(e)}"
    
    def get_conversation_history(self) -> str:
        """Get formatted conversation history"""
        if not self.conversation_history:
            return "No conversation history"
        
        formatted = []
        for i, conv in enumerate(self.conversation_history[-5:], 1):  # Show last 5
            formatted.append(f"""
**Q{i}:** {conv['query']}
**A{i}:** {conv['answer'][:200]}{'...' if len(conv['answer']) > 200 else ''}
*Method: {conv['method']} | Time: {conv['timestamp']}*
---
""")
        
        return "\n".join(formatted)
    
    def clear_conversation(self) -> str:
        """Clear conversation history"""
        self.conversation_history = []
        return "Conversation history cleared"
    
    def create_interface(self):
        """Create simplified Gradio interface"""
        
        with gr.Blocks(title="Simple RAG with LangChain & LlamaIndex", theme=gr.themes.Soft()) as interface:
            
            gr.Markdown("""
            # 🚀 Simple RAG Pipeline
            ## LangChain + LlamaIndex + Docling + Supabase
            
            Upload PDF documents and query them using advanced RAG techniques.
            """)
            
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("### 📄 Document Processing")
                    
                    file_upload = gr.File(
                        label="Upload PDF Files",
                        file_count="multiple",
                        file_types=[".pdf"]
                    )
                    
                    provider_choice = gr.Radio(
                        choices=["openai", "anthropic"],
                        value="openai",
                        label="LLM Provider"
                    )
                    
                    process_btn = gr.Button("🔄 Process Documents", variant="primary")
                    
                    processing_status = gr.Textbox(
                        label="Status",
                        lines=8,
                        interactive=False
                    )
                
                with gr.Column(scale=1):
                    gr.Markdown("### 🔍 Query Documents")
                    
                    query_input = gr.Textbox(
                        label="Enter your question",
                        placeholder="What is this document about?",
                        lines=2
                    )
                    
                    query_method = gr.Radio(
                        choices=["langchain", "llama_index", "hybrid"],
                        value="langchain",
                        label="Query Method"
                    )
                    
                    query_btn = gr.Button("🔍 Query", variant="primary")
                    
                    answer_output = gr.Textbox(
                        label="Answer",
                        lines=6,
                        interactive=False
                    )
                    
                    sources_output = gr.Textbox(
                        label="Sources",
                        lines=4,
                        interactive=False
                    )
            
            with gr.Row():
                with gr.Column():
                    gr.Markdown("### 💬 Conversation History")
                    
                    conversation_display = gr.Textbox(
                        label="Recent Conversations",
                        lines=8,
                        interactive=False
                    )
                    
                    with gr.Row():
                        refresh_btn = gr.Button("🔄 Refresh")
                        clear_btn = gr.Button("🗑️ Clear")
            
            # Event handlers
            process_btn.click(
                fn=self.process_files_sync,
                inputs=[file_upload, provider_choice],
                outputs=[processing_status, gr.State()]
            )
            
            query_btn.click(
                fn=self.query_documents_sync,
                inputs=[query_input, query_method],
                outputs=[answer_output, sources_output]
            )
            
            refresh_btn.click(
                fn=self.get_conversation_history,
                outputs=[conversation_display]
            )
            
            clear_btn.click(
                fn=self.clear_conversation,
                outputs=[conversation_display]
            )
        
        return interface

def main():
    """Main function to run the application"""
    try:
        # Create and launch app
        app = SimpleRAGApp()
        interface = app.create_interface()
        
        # Launch with custom settings
        interface.launch(
            server_name="0.0.0.0",
            server_port=7861,
            share=False,
            debug=False,
            show_error=True
        )
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        raise

if __name__ == "__main__":
    main()
