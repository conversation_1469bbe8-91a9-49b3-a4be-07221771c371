# Gradio Docling RAG Pipeline

A comprehensive Retrieval-Augmented Generation (RAG) system that processes PDF documents using Docling, stores embeddings in Supabase PostgreSQL with vector search, and provides an interactive Gradio web interface for document querying.

## 📌 System Architecture

The system consists of three main pipelines:

### 🔄 Document Processing Pipeline
1. **PDF Parsing** → Uses [Docling](https://docling-project.github.io/docling/) for advanced PDF processing
2. **Markdown Extraction** → Converts documents to structured markdown
3. **Embedding Generation** → Creates vector embeddings using OpenAI/Mistral
4. **Vector Storage** → Stores in Supabase PostgreSQL with pgvector

### ⚙️ Output Generation Pipeline
1. **Query Processing** → Accepts natural language queries
2. **Vector Search** → Finds relevant document chunks using similarity search
3. **Prompt Enrichment** → Enhances queries with matching context
4. **JSON Response** → Returns structured responses via LLM

### 🧹 Post Processing Pipeline
1. **Format Conversion** → JSON to CSV export functionality
2. **Data Cleaning** → Removes invalid or corrupted entries
3. **Quality Control** → Validates response integrity

## 📊 Data Schemas

### File-Level Metadata
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename TEXT NOT NULL,
    markdown_data TEXT,
    title TEXT,
    summary TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Page-Level Metadata
```sql
CREATE TABLE document_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_ref_id UUID REFERENCES documents(id),
    page_number INTEGER,
    base_64_encoded_data TEXT,
    base_64_encoded_images TEXT[], -- Array of base64 images
    markdown TEXT,
    embedding VECTOR(1536), -- OpenAI embedding dimension
    summary TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Supabase account with pgvector extension enabled
- API keys for OpenAI/Mistral/Anthropic

### Installation

1. **Clone and setup environment:**
```bash
git clone <repository-url>
cd gradio-docling-rag
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your API keys and Supabase credentials
```

3. **Initialize database:**
```bash
python setup_database.py
```

4. **Launch the application:**
```bash
python app.py
```

## 🔧 Configuration

### Environment Variables
```env
# API Keys
OPENAI_API_KEY=your_openai_key
MISTRAL_API_KEY=your_mistral_key
ANTHROPIC_API_KEY=your_anthropic_key

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_service_key

# Application Settings
EMBEDDING_MODEL=text-embedding-3-small
LLM_MODEL=gpt-4o-mini
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

## 📁 Project Structure

```
gradio-docling-rag/
├── app.py                 # Main Gradio application
├── requirements.txt       # Python dependencies
├── setup_database.py      # Database initialization
├── .env.example          # Environment template
├── README.md             # This file
├── sample/               # Sample PDF documents
│   ├── M-500 MAIN AIR COMPRESSOR.pdf
│   └── TANK CLEANING MACHINE.pdf
├── src/
│   ├── __init__.py
│   ├── document_processor.py    # PDF processing with Docling
│   ├── vector_store.py         # Supabase vector operations
│   ├── llm_handler.py          # LLM interactions
│   ├── post_processor.py       # Data cleaning and export
│   └── utils.py               # Utility functions
└── tests/
    ├── __init__.py
    ├── test_document_processor.py
    ├── test_vector_store.py
    └── test_integration.py
```

## 🎯 Features

### Document Processing
- **Multi-format Support**: PDF, scanned documents, digital documents
- **Advanced OCR**: Handles complex layouts and tables
- **Metadata Extraction**: Automatic title, summary, and structure detection
- **Image Processing**: Extracts and encodes embedded images

### Vector Search
- **Semantic Search**: Find relevant content using natural language
- **Hybrid Search**: Combines keyword and vector similarity
- **Contextual Retrieval**: Returns relevant chunks with context
- **Similarity Scoring**: Ranked results with confidence scores

### Web Interface
- **Drag & Drop Upload**: Easy document upload interface
- **Real-time Processing**: Live progress updates
- **Interactive Chat**: Query documents conversationally
- **Export Options**: Download results as JSON/CSV
- **Document Management**: View, delete, and organize uploaded files

## 🔍 Usage Examples

### Document Upload and Processing
1. Launch the Gradio interface
2. Upload PDF documents via drag-and-drop
3. Monitor processing progress in real-time
4. View extracted content and metadata

### Querying Documents
```python
# Example queries:
"Find all safety procedures mentioned in the documents"
"What are the maintenance requirements for the air compressor?"
"List all technical specifications from the tank cleaning machine manual"
```

### API Usage
```python
from src.document_processor import DocumentProcessor
from src.vector_store import VectorStore

# Process a document
processor = DocumentProcessor()
result = processor.process_pdf("sample/M-500 MAIN AIR COMPRESSOR.pdf")

# Query the vector store
vector_store = VectorStore()
results = vector_store.similarity_search("maintenance procedures", k=5)
```

## 🧪 Testing

Run the test suite:
```bash
pytest tests/ -v
```

Test with sample documents:
```bash
python -m pytest tests/test_integration.py::test_sample_documents
```

## 📈 Performance Optimization

- **Batch Processing**: Process multiple documents simultaneously
- **Caching**: Redis integration for frequently accessed embeddings
- **Async Operations**: Non-blocking document processing
- **Connection Pooling**: Optimized database connections

## 🛠️ Troubleshooting

### Common Issues
1. **PDF Processing Errors**: Ensure Docling dependencies are installed
2. **Vector Search Slow**: Check pgvector index configuration
3. **Memory Issues**: Adjust chunk size and batch processing limits
4. **API Rate Limits**: Implement exponential backoff

### Debug Mode
```bash
export DEBUG=1
python app.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🔗 Resources

- [Docling Documentation](https://docling-project.github.io/docling/)
- [Supabase Vector Guide](https://supabase.com/docs/guides/ai/vector-embeddings)
- [Gradio Documentation](https://gradio.app/docs/)
