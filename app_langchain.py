"""
Advanced RAG Application using LangChain and LlamaIndex
Gradio interface for document processing and querying
"""

import os
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
import gradio as gr
from pathlib import Path
import json
import pandas as pd

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import our advanced processor
from src.langchain_processor import AdvancedRAGProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedRAGApp:
    """Advanced RAG Application with Gradio Interface"""
    
    def __init__(self):
        """Initialize the application"""
        self.processor = AdvancedRAGProcessor()
        self.processed_files = []
        self.conversation_history = []
        
        # Check environment variables
        self._check_environment()
        
        logger.info("Advanced RAG App initialized")
    
    def _check_environment(self):
        """Check if all required environment variables are set"""
        required_vars = [
            "SUPABASE_URL",
            "SUPABASE_KEY", 
            "OPENAI_API_KEY"
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("Environment variables validated")
    
    def process_files(self, files: List[str], provider: str = "openai") -> Tuple[str, str]:
        """Process uploaded files"""
        try:
            if not files:
                return "❌ No files provided", ""

            # Filter PDF files
            pdf_files = [f for f in files if f.lower().endswith('.pdf')]

            if not pdf_files:
                return "❌ No PDF files found", ""

            logger.info(f"Processing {len(pdf_files)} PDF files")

            # Process files using asyncio
            results = asyncio.run(self.processor.process_multiple_pdfs(pdf_files))

            # Setup chains
            self.processor.setup_chains(provider)

            # Update processed files list
            self.processed_files.extend(pdf_files)

            # Create status message
            status_msg = f"""
✅ **Processing Complete!**

📊 **Summary:**
- Total files processed: {len(results['processed'])}
- Total documents extracted: {results['total_documents']}
- Failed files: {len(results['failed'])}

📁 **Processed Files:**
{chr(10).join([f"• {Path(item['file']).name} ({item['documents_count']} chunks)" for item in results['processed']])}

{f"❌ **Failed Files:**{chr(10)}{chr(10).join([f'• {Path(item['file']).name}: {item['error']}' for item in results['failed']])}" if results['failed'] else ""}

🤖 **LLM Provider:** {provider.upper()}
🔍 **Ready for queries!**
"""

            # Create file list for display
            file_list = "\n".join([f"📄 {Path(f).name}" for f in self.processed_files])

            return status_msg, file_list

        except Exception as e:
            error_msg = f"❌ Error processing files: {str(e)}"
            logger.error(error_msg)
            return error_msg, ""
    
    def query_documents(self, query: str, method: str = "langchain", use_conversation: bool = False) -> Tuple[str, str, str]:
        """Query processed documents"""
        try:
            if not query.strip():
                return "❌ Please enter a query", "", ""

            if not self.processed_files:
                return "❌ No documents processed. Please upload and process documents first.", "", ""

            logger.info(f"Querying with method: {method}, query: {query[:100]}...")

            # Execute query based on method using asyncio
            if method == "langchain":
                result = asyncio.run(self.processor.query_documents(query, use_conversation))
            elif method == "llama_index":
                result = asyncio.run(self.processor.query_with_llama_index(query))
            elif method == "hybrid":
                result = asyncio.run(self.processor.hybrid_query(query))
            else:
                return "❌ Invalid query method", "", ""

            # Format response
            if result.get("error"):
                return f"❌ {result.get('answer', 'Unknown error')}", "", ""

            # Format answer
            if method == "hybrid":
                answer = result.get("combined_answer", "")
            else:
                answer = result.get("answer", "")

            # Format sources
            sources_info = self._format_sources(result, method)

            # Update conversation history
            self.conversation_history.append({
                "query": query,
                "answer": answer,
                "method": method,
                "timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
            })

            # Create conversation display
            conversation_display = self._format_conversation()

            return answer, sources_info, conversation_display

        except Exception as e:
            error_msg = f"❌ Error querying documents: {str(e)}"
            logger.error(error_msg)
            return error_msg, "", ""
    
    def _format_sources(self, result: Dict[str, Any], method: str) -> str:
        """Format source information"""
        try:
            if method == "langchain":
                sources = result.get("source_documents", [])
                if not sources:
                    return "No sources found"
                
                formatted_sources = []
                for i, source in enumerate(sources[:5], 1):  # Limit to top 5
                    formatted_sources.append(f"""
**Source {i}:**
- File: {source.get('metadata', {}).get('filename', 'Unknown')}
- Page: {source.get('page', 'Unknown')}
- Content: {source.get('content', '')[:200]}...
""")
                return "\n".join(formatted_sources)
            
            elif method == "llama_index":
                sources = result.get("source_nodes", [])
                if not sources:
                    return "No sources found"
                
                formatted_sources = []
                for i, source in enumerate(sources[:5], 1):
                    formatted_sources.append(f"""
**Source {i}:** (Score: {source.get('score', 0):.3f})
- Content: {source.get('content', '')[:200]}...
""")
                return "\n".join(formatted_sources)
            
            elif method == "hybrid":
                combined_sources = result.get("combined_sources", {})
                langchain_sources = combined_sources.get("langchain_sources", [])
                llama_sources = combined_sources.get("llama_index_sources", [])
                
                formatted = []
                
                if langchain_sources:
                    formatted.append("**LangChain Sources:**")
                    for i, source in enumerate(langchain_sources[:3], 1):
                        formatted.append(f"  {i}. {source.get('metadata', {}).get('filename', 'Unknown')} (Page {source.get('page', 'Unknown')})")
                
                if llama_sources:
                    formatted.append("\n**LlamaIndex Sources:**")
                    for i, source in enumerate(llama_sources[:3], 1):
                        formatted.append(f"  {i}. Score: {source.get('score', 0):.3f}")
                
                return "\n".join(formatted)
            
            return "No sources available"
            
        except Exception as e:
            return f"Error formatting sources: {str(e)}"
    
    def _format_conversation(self) -> str:
        """Format conversation history"""
        if not self.conversation_history:
            return "No conversation history"
        
        formatted = []
        for i, conv in enumerate(self.conversation_history[-10:], 1):  # Show last 10
            formatted.append(f"""
**Q{i}:** {conv['query']}
**A{i}:** {conv['answer'][:300]}{'...' if len(conv['answer']) > 300 else ''}
*Method: {conv['method']} | Time: {conv['timestamp']}*
---
""")
        
        return "\n".join(formatted)
    
    def clear_conversation(self) -> str:
        """Clear conversation history"""
        self.conversation_history = []
        return "Conversation history cleared"
    
    def export_conversation(self) -> str:
        """Export conversation to JSON"""
        try:
            if not self.conversation_history:
                return "No conversation to export"
            
            export_data = {
                "conversation_history": self.conversation_history,
                "processed_files": [Path(f).name for f in self.processed_files],
                "export_timestamp": pd.Timestamp.now().isoformat()
            }
            
            filename = f"conversation_export_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            return f"✅ Conversation exported to {filename}"
            
        except Exception as e:
            return f"❌ Error exporting conversation: {str(e)}"
    
    def create_interface(self):
        """Create Gradio interface"""
        
        with gr.Blocks(title="Advanced RAG with LangChain & LlamaIndex", theme=gr.themes.Soft()) as interface:
            
            gr.Markdown("""
            # 🚀 Advanced RAG Pipeline
            ## LangChain + LlamaIndex + Docling + Supabase
            
            Upload PDF documents and query them using state-of-the-art RAG techniques.
            """)
            
            with gr.Tabs():
                
                # Document Processing Tab
                with gr.Tab("📄 Document Processing"):
                    gr.Markdown("### Upload and Process Documents")
                    
                    with gr.Row():
                        with gr.Column():
                            file_upload = gr.File(
                                label="Upload PDF Files",
                                file_count="multiple",
                                file_types=[".pdf"]
                            )
                            
                            provider_choice = gr.Radio(
                                choices=["openai", "anthropic"],
                                value="openai",
                                label="LLM Provider"
                            )
                            
                            process_btn = gr.Button("🔄 Process Documents", variant="primary")
                        
                        with gr.Column():
                            processing_status = gr.Textbox(
                                label="Processing Status",
                                lines=10,
                                interactive=False
                            )
                            
                            file_list = gr.Textbox(
                                label="Processed Files",
                                lines=5,
                                interactive=False
                            )
                    
                    # Process button click
                    process_btn.click(
                        fn=self.process_files,
                        inputs=[file_upload, provider_choice],
                        outputs=[processing_status, file_list]
                    )
                
                # Query Tab
                with gr.Tab("🔍 Query Documents"):
                    gr.Markdown("### Query Your Documents")
                    
                    with gr.Row():
                        with gr.Column():
                            query_input = gr.Textbox(
                                label="Enter your question",
                                placeholder="What is this document about?",
                                lines=3
                            )
                            
                            with gr.Row():
                                query_method = gr.Radio(
                                    choices=["langchain", "llama_index", "hybrid"],
                                    value="langchain",
                                    label="Query Method"
                                )
                                
                                use_conversation = gr.Checkbox(
                                    label="Use Conversation Memory",
                                    value=False
                                )
                            
                            query_btn = gr.Button("🔍 Query", variant="primary")
                        
                        with gr.Column():
                            answer_output = gr.Textbox(
                                label="Answer",
                                lines=8,
                                interactive=False
                            )
                            
                            sources_output = gr.Textbox(
                                label="Sources",
                                lines=6,
                                interactive=False
                            )
                    
                    # Query button click
                    query_btn.click(
                        fn=self.query_documents,
                        inputs=[query_input, query_method, use_conversation],
                        outputs=[answer_output, sources_output, gr.State()]
                    )
                
                # Conversation Tab
                with gr.Tab("💬 Conversation History"):
                    gr.Markdown("### Conversation Management")
                    
                    conversation_display = gr.Textbox(
                        label="Conversation History",
                        lines=15,
                        interactive=False
                    )
                    
                    with gr.Row():
                        refresh_btn = gr.Button("🔄 Refresh")
                        clear_btn = gr.Button("🗑️ Clear History")
                        export_btn = gr.Button("💾 Export")
                    
                    export_status = gr.Textbox(
                        label="Export Status",
                        lines=2,
                        interactive=False
                    )
                    
                    # Button clicks
                    refresh_btn.click(
                        fn=self._format_conversation,
                        outputs=[conversation_display]
                    )
                    
                    clear_btn.click(
                        fn=self.clear_conversation,
                        outputs=[conversation_display]
                    )
                    
                    export_btn.click(
                        fn=self.export_conversation,
                        outputs=[export_status]
                    )
                
                # System Info Tab
                with gr.Tab("ℹ️ System Info"):
                    gr.Markdown("""
                    ### System Information
                    
                    **Technologies Used:**
                    - 🦜 **LangChain**: Document loading, text splitting, vector stores, chains
                    - 🦙 **LlamaIndex**: Advanced indexing and query engines
                    - 📄 **Docling**: Advanced PDF processing with OCR and table extraction
                    - 🗄️ **Supabase**: PostgreSQL with pgvector for vector storage
                    - 🤖 **Multi-LLM**: OpenAI GPT-4o-mini, Anthropic Claude support
                    - 🌐 **Gradio**: Interactive web interface
                    
                    **Query Methods:**
                    - **LangChain**: Uses RetrievalQA and ConversationalRetrievalChain
                    - **LlamaIndex**: Uses VectorStoreIndex with query engines
                    - **Hybrid**: Combines both approaches for comprehensive results
                    
                    **Features:**
                    - Advanced PDF processing with OCR
                    - Conversation memory
                    - Multiple LLM providers
                    - Source attribution
                    - Export capabilities
                    """)
        
        return interface

def main():
    """Main function to run the application"""
    try:
        # Create and launch app
        app = AdvancedRAGApp()
        interface = app.create_interface()
        
        # Launch with custom settings
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=True,
            show_error=True
        )
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        raise

if __name__ == "__main__":
    main()
