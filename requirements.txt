# Core Dependencies
gradio>=4.0.0
docling>=1.0.0
python-dotenv>=1.0.0
supabase>=2.0.0
psycopg2-binary>=2.9.0

# AI/ML Dependencies - LangChain & LlamaIndex
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-anthropic>=0.1.0
langchain-community>=0.0.20
llama-index>=0.10.0
llama-index-llms-openai>=0.1.0
llama-index-llms-anthropic>=0.1.0
llama-index-embeddings-openai>=0.1.0
llama-index-vector-stores-supabase>=0.1.0
openai>=1.0.0
anthropic>=0.25.0
sentence-transformers>=2.2.0
numpy>=1.24.0
pandas>=2.0.0

# Document Processing
PyPDF2>=3.0.0
pdf2image>=1.16.0
Pillow>=10.0.0
python-magic>=0.4.27

# Vector Operations
pgvector>=0.2.0
faiss-cpu>=1.7.0

# Web Framework
fastapi>=0.100.0
uvicorn>=0.23.0

# Utilities
requests>=2.31.0
aiohttp>=3.8.0
asyncio-throttle>=1.0.0
tqdm>=4.65.0
pydantic>=2.0.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Optional: For advanced features
redis>=4.6.0
celery>=5.3.0
streamlit>=1.25.0  # Alternative UI option
