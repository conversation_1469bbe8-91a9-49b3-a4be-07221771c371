#!/usr/bin/env python3
"""
Test script to validate the RAG pipeline setup
Run this to check if all components are working correctly
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import validate_environment, setup_logging
from src.document_processor import DocumentProcessor, validate_pdf_file
from src.vector_store import VectorStore
from src.llm_handler import LLMHandler
from src.post_processor import PostProcessor

# Setup logging
setup_logging("INFO")
logger = logging.getLogger(__name__)

async def test_document_processing():
    """Test document processing with sample PDFs"""
    logger.info("🔍 Testing document processing...")
    
    try:
        processor = DocumentProcessor()
        sample_dir = Path("sample")
        
        if not sample_dir.exists():
            logger.error("Sample directory not found")
            return False
        
        pdf_files = list(sample_dir.glob("*.pdf"))
        if not pdf_files:
            logger.error("No PDF files found in sample directory")
            return False
        
        # Test with first PDF
        test_pdf = pdf_files[0]
        logger.info(f"Processing: {test_pdf.name}")
        
        # Validate PDF
        if not validate_pdf_file(str(test_pdf)):
            logger.error(f"Invalid PDF file: {test_pdf}")
            return False
        
        # Process document
        result = await processor.process_pdf(str(test_pdf))
        
        if result.get("processing_status") == "success":
            logger.info(f"✅ Successfully processed {test_pdf.name}")
            logger.info(f"   - Pages: {result['metadata'].get('num_pages', 0)}")
            logger.info(f"   - Title: {result['metadata'].get('title', 'N/A')}")
            logger.info(f"   - Chunks: {len(result.get('chunks', []))}")
            return True
        else:
            logger.error(f"❌ Failed to process {test_pdf.name}: {result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Document processing test failed: {str(e)}")
        return False

async def test_vector_store():
    """Test vector store connection"""
    logger.info("🔍 Testing vector store connection...")
    
    try:
        vector_store = VectorStore()
        
        # Test basic connection
        documents = await vector_store.list_documents(limit=1)
        logger.info(f"✅ Vector store connection successful")
        logger.info(f"   - Found {len(documents)} existing documents")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector store test failed: {str(e)}")
        logger.error("   Make sure Supabase is configured correctly")
        return False

async def test_llm_handler():
    """Test LLM handler"""
    logger.info("🔍 Testing LLM handler...")
    
    try:
        llm_handler = LLMHandler()
        
        # Test with simple context
        test_context = [{
            "content": "This is a test document about safety procedures.",
            "filename": "test.pdf",
            "similarity_score": 0.9
        }]
        
        response = await llm_handler.generate_response(
            query="What safety procedures are mentioned?",
            context_chunks=test_context,
            response_format="json"
        )
        
        if response and not response.get("error"):
            logger.info("✅ LLM handler working correctly")
            return True
        else:
            logger.error(f"❌ LLM handler failed: {response.get('response', {}).get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ LLM handler test failed: {str(e)}")
        logger.error("   Check your API keys in .env file")
        return False

def test_post_processor():
    """Test post processor"""
    logger.info("🔍 Testing post processor...")
    
    try:
        post_processor = PostProcessor()
        
        # Test data cleaning
        test_data = {
            "answer": "Test answer",
            "confidence": "high",
            "empty_field": "",
            "null_field": None
        }
        
        cleaned = post_processor.clean_response_data(test_data)
        
        if cleaned and "answer" in cleaned and "_metadata" in cleaned:
            logger.info("✅ Post processor working correctly")
            return True
        else:
            logger.error("❌ Post processor failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Post processor test failed: {str(e)}")
        return False

async def test_complete_workflow():
    """Test the complete workflow with a sample document"""
    logger.info("🔍 Testing complete workflow...")
    
    try:
        # Initialize components
        processor = DocumentProcessor()
        vector_store = VectorStore()
        llm_handler = LLMHandler()
        post_processor = PostProcessor()
        
        # Get sample PDF
        sample_dir = Path("sample")
        pdf_files = list(sample_dir.glob("*.pdf"))
        
        if not pdf_files:
            logger.error("No sample PDFs found")
            return False
        
        test_pdf = pdf_files[0]
        logger.info(f"Testing workflow with: {test_pdf.name}")
        
        # Step 1: Process document
        doc_result = await processor.process_pdf(str(test_pdf))
        if doc_result.get("processing_status") != "success":
            logger.error("Document processing failed in workflow test")
            return False
        
        # Step 2: Store in vector database
        doc_id = await vector_store.store_document(doc_result)
        if not doc_id:
            logger.error("Document storage failed in workflow test")
            return False
        
        logger.info(f"   - Document stored with ID: {doc_id[:8]}...")
        
        # Step 3: Search
        search_results = await vector_store.similarity_search("safety", k=2)
        if not search_results:
            logger.warning("No search results found - this might be normal for test documents")
        
        logger.info(f"   - Found {len(search_results)} search results")
        
        # Step 4: Generate response (if we have results)
        if search_results:
            response = await llm_handler.generate_response(
                query="What safety information is available?",
                context_chunks=search_results,
                response_format="json"
            )
            
            if response and not response.get("error"):
                logger.info("   - LLM response generated successfully")
                
                # Step 5: Post-process
                cleaned = post_processor.clean_response_data(response)
                if cleaned:
                    logger.info("   - Response post-processed successfully")
        
        # Clean up: Delete test document
        await vector_store.delete_document(doc_id)
        logger.info("   - Test document cleaned up")
        
        logger.info("✅ Complete workflow test successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Complete workflow test failed: {str(e)}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting RAG Pipeline Setup Tests")
    logger.info("=" * 50)
    
    # Check environment
    try:
        validate_environment()
        logger.info("✅ Environment validation passed")
    except Exception as e:
        logger.error(f"❌ Environment validation failed: {str(e)}")
        logger.error("Please check your .env file configuration")
        return
    
    # Run tests
    tests = [
        ("Document Processing", test_document_processing),
        ("Vector Store", test_vector_store),
        ("LLM Handler", test_llm_handler),
        ("Post Processor", test_post_processor),
        ("Complete Workflow", test_complete_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    logger.info("=" * 50)
    logger.info(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Your RAG pipeline is ready to use.")
        logger.info("\nNext steps:")
        logger.info("1. Run: python app.py")
        logger.info("2. Open your browser to http://localhost:7860")
        logger.info("3. Upload PDFs and start querying!")
    else:
        logger.error("⚠️  Some tests failed. Please check the errors above.")
        logger.error("Make sure all dependencies are installed and configuration is correct.")

if __name__ == "__main__":
    asyncio.run(main())
