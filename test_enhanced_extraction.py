#!/usr/bin/env python3
"""
Test script for enhanced table extraction with improved bbox detection
"""

import asyncio
import logging
from pathlib import Path
from src.table_extractor import AdvancedTableExtractor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhanced_extraction():
    """Test the enhanced table extraction system"""
    
    logger.info("🚀 Testing enhanced table extraction...")
    
    # Initialize extractor
    extractor = AdvancedTableExtractor()
    
    # Test with the problematic PDF
    pdf_path = "sample/M-500 MAIN AIR COMPRESSOR.pdf"
    output_dir = "output_enhanced"
    
    if not Path(pdf_path).exists():
        logger.error(f"❌ PDF file not found: {pdf_path}")
        return
    
    try:
        # Extract tables
        result = await extractor.extract_tables_from_pdf(pdf_path, output_dir)
        
        logger.info(f"📊 Extraction Results:")
        logger.info(f"  - Status: {result['status']}")
        logger.info(f"  - Tables found: {len(result['tables'])}")
        logger.info(f"  - Spare parts: {len(result['spare_parts'])}")
        
        # Check each table
        for i, table in enumerate(result['tables'], 1):
            logger.info(f"  📋 Table {i}:")
            logger.info(f"    - Page: {table['page_number']}")
            logger.info(f"    - CSV length: {len(table['csv_content'])} chars")
            logger.info(f"    - Image: {table['image_path']}")
            logger.info(f"    - Bbox: {table['bbox']}")
            
            # Check if image was successfully created
            if table['image_path']:
                image_path = Path(table['image_path'])
                if image_path.exists():
                    size = image_path.stat().st_size
                    logger.info(f"    - Image size: {size:,} bytes ✅")
                else:
                    logger.warning(f"    - Image file missing ⚠️")
            else:
                logger.warning(f"    - No image created ⚠️")
        
        # Save files
        saved_files = extractor.save_tables_to_files(result, output_dir)
        logger.info(f"📁 Saved {len(saved_files)} files to {output_dir}/")
        
        logger.info("✅ Enhanced extraction test completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_extraction())
