"""
Test script for LangChain + LlamaIndex RAG system
"""

import os
import asyncio
import logging
from pathlib import Path

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from src.langchain_processor import AdvancedRAGProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_advanced_rag():
    """Test the advanced RAG system"""
    
    print("🚀 Testing Advanced RAG System with LangChain + LlamaIndex")
    print("=" * 60)
    
    try:
        # Initialize processor
        print("1. Initializing Advanced RAG Processor...")
        processor = AdvancedRAGProcessor()
        print("✅ Processor initialized successfully")
        
        # Check for sample files
        sample_dir = Path("sample")
        if not sample_dir.exists():
            print("❌ Sample directory not found. Please add PDF files to test.")
            return
        
        pdf_files = list(sample_dir.glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found in sample directory.")
            return
        
        print(f"📄 Found {len(pdf_files)} PDF files:")
        for pdf in pdf_files:
            print(f"   - {pdf.name}")
        
        # Process documents
        print("\n2. Processing documents with Docling...")
        results = await processor.process_multiple_pdfs([str(f) for f in pdf_files])
        
        print(f"✅ Processing complete:")
        print(f"   - Processed: {len(results['processed'])} files")
        print(f"   - Total documents: {results['total_documents']}")
        print(f"   - Failed: {len(results['failed'])} files")
        
        if results['failed']:
            print("❌ Failed files:")
            for failed in results['failed']:
                print(f"   - {failed['file']}: {failed['error']}")
        
        if results['total_documents'] == 0:
            print("❌ No documents were processed successfully.")
            return
        
        # Setup chains
        print("\n3. Setting up LangChain and LlamaIndex...")
        processor.setup_chains("openai")
        print("✅ Chains setup complete")
        
        # Test queries
        test_queries = [
            "What is this document about?",
            "What are the main components described?",
            "What are the technical specifications?"
        ]
        
        print("\n4. Testing queries...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- Query {i}: {query} ---")
            
            # Test LangChain
            print("🦜 LangChain Response:")
            lc_result = await processor.query_documents(query, use_conversation=False)
            if not lc_result.get("error"):
                print(f"Answer: {lc_result['answer'][:200]}...")
                print(f"Sources: {len(lc_result['source_documents'])} documents")
            else:
                print(f"Error: {lc_result['answer']}")
            
            # Test LlamaIndex
            print("\n🦙 LlamaIndex Response:")
            li_result = await processor.query_with_llama_index(query)
            if not li_result.get("error"):
                print(f"Answer: {li_result['answer'][:200]}...")
                print(f"Sources: {len(li_result['source_nodes'])} nodes")
            else:
                print(f"Error: {li_result['answer']}")
            
            # Test Hybrid
            print("\n🔄 Hybrid Response:")
            hybrid_result = await processor.hybrid_query(query)
            if not hybrid_result.get("error"):
                print(f"Combined Answer: {hybrid_result['combined_answer'][:200]}...")
            else:
                print(f"Error: {hybrid_result['combined_answer']}")
            
            print("-" * 50)
        
        print("\n🎉 All tests completed successfully!")
        print("The advanced RAG system is working correctly.")
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        print(f"❌ Test failed: {str(e)}")
        raise

def main():
    """Main function"""
    try:
        # Check environment variables
        required_vars = ["SUPABASE_URL", "SUPABASE_KEY", "OPENAI_API_KEY"]
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"❌ Missing environment variables: {missing_vars}")
            print("Please check your .env file")
            return
        
        # Run async test
        asyncio.run(test_advanced_rag())
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        logger.error(f"Test failed: {str(e)}")

if __name__ == "__main__":
    main()
