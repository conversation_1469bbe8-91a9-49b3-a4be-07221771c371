"""
Integration tests for the RAG pipeline
Tests the complete workflow with sample documents
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from document_processor import DocumentProcessor
from vector_store import VectorStore
from llm_handler import LLMHandler
from post_processor import PostProcessor

class TestRAGPipelineIntegration:
    """Integration tests for the complete RAG pipeline"""
    
    @pytest.fixture(scope="class")
    def sample_pdf_paths(self):
        """Get paths to sample PDF files"""
        sample_dir = Path(__file__).parent.parent / "sample"
        pdf_files = list(sample_dir.glob("*.pdf"))
        
        if not pdf_files:
            pytest.skip("No sample PDF files found")
        
        return [str(pdf) for pdf in pdf_files]
    
    @pytest.fixture(scope="class")
    def document_processor(self):
        """Create document processor instance"""
        return DocumentProcessor()
    
    @pytest.fixture(scope="class")
    def vector_store(self):
        """Create vector store instance"""
        try:
            return VectorStore()
        except Exception as e:
            pytest.skip(f"Could not initialize vector store: {e}")
    
    @pytest.fixture(scope="class")
    def llm_handler(self):
        """Create LLM handler instance"""
        return LLMHandler()
    
    @pytest.fixture(scope="class")
    def post_processor(self):
        """Create post processor instance"""
        return PostProcessor()
    
    @pytest.mark.asyncio
    async def test_document_processing(self, document_processor, sample_pdf_paths):
        """Test document processing with sample PDFs"""
        for pdf_path in sample_pdf_paths:
            result = await document_processor.process_pdf(pdf_path)
            
            assert result is not None
            assert "metadata" in result
            assert "markdown_data" in result
            assert result["metadata"]["filename"] == Path(pdf_path).name
            
            if result.get("processing_status") == "success":
                assert len(result.get("pages", [])) > 0
                assert len(result.get("chunks", [])) > 0
                assert result.get("summary") is not None
    
    @pytest.mark.asyncio
    async def test_vector_storage(self, document_processor, vector_store, sample_pdf_paths):
        """Test storing documents in vector database"""
        for pdf_path in sample_pdf_paths[:1]:  # Test with first PDF only
            # Process document
            doc_result = await document_processor.process_pdf(pdf_path)
            
            if doc_result.get("processing_status") == "success":
                # Store in vector database
                doc_id = await vector_store.store_document(doc_result)
                
                assert doc_id is not None
                assert len(doc_id) > 0
                
                # Verify document was stored
                stored_doc = await vector_store.get_document(doc_id)
                assert stored_doc is not None
                assert stored_doc["filename"] == doc_result["metadata"]["filename"]
    
    @pytest.mark.asyncio
    async def test_similarity_search(self, vector_store):
        """Test similarity search functionality"""
        # Test queries
        test_queries = [
            "safety procedures",
            "maintenance requirements",
            "technical specifications",
            "operating instructions"
        ]
        
        for query in test_queries:
            results = await vector_store.similarity_search(query, k=3)
            
            # Results should be a list
            assert isinstance(results, list)
            
            # If results exist, they should have required fields
            for result in results:
                assert "content" in result
                assert "similarity_score" in result
                assert "filename" in result
                assert isinstance(result["similarity_score"], (int, float))
    
    @pytest.mark.asyncio
    async def test_llm_response_generation(self, llm_handler, vector_store):
        """Test LLM response generation with context"""
        # Get some context from vector search
        search_results = await vector_store.similarity_search("safety", k=2)
        
        if search_results:
            # Generate response
            response = await llm_handler.generate_response(
                query="What safety measures are mentioned?",
                context_chunks=search_results,
                response_format="json"
            )
            
            assert response is not None
            assert "response" in response
            assert "query" in response
            assert response["query"] == "What safety measures are mentioned?"
    
    @pytest.mark.asyncio
    async def test_post_processing(self, post_processor):
        """Test post-processing functionality"""
        # Test data cleaning
        test_data = {
            "answer": "This is a test answer",
            "key_points": ["Point 1", "Point 2"],
            "confidence": "high",
            "empty_field": "",
            "null_field": None
        }
        
        cleaned_data = post_processor.clean_response_data(test_data)
        
        assert cleaned_data is not None
        assert "answer" in cleaned_data
        assert "key_points" in cleaned_data
        assert "empty_field" not in cleaned_data  # Should be removed
        assert "null_field" not in cleaned_data   # Should be removed
        assert "_metadata" in cleaned_data        # Should be added
    
    @pytest.mark.asyncio
    async def test_json_to_csv_conversion(self, post_processor):
        """Test JSON to CSV conversion"""
        test_data = [
            {
                "query": "Test query 1",
                "answer": "Test answer 1",
                "confidence": 0.8
            },
            {
                "query": "Test query 2", 
                "answer": "Test answer 2",
                "confidence": 0.9
            }
        ]
        
        csv_path = post_processor.json_to_csv(test_data, "test_export")
        
        assert os.path.exists(csv_path)
        assert csv_path.endswith(".csv")
        
        # Clean up
        os.remove(csv_path)
    
    @pytest.mark.asyncio
    async def test_complete_pipeline_workflow(self, document_processor, vector_store, 
                                            llm_handler, post_processor, sample_pdf_paths):
        """Test the complete pipeline workflow"""
        if not sample_pdf_paths:
            pytest.skip("No sample PDFs available")
        
        # Step 1: Process a document
        pdf_path = sample_pdf_paths[0]
        doc_result = await document_processor.process_pdf(pdf_path)
        
        if doc_result.get("processing_status") != "success":
            pytest.skip(f"Document processing failed: {doc_result.get('error')}")
        
        # Step 2: Store in vector database
        doc_id = await vector_store.store_document(doc_result)
        assert doc_id is not None
        
        # Step 3: Search for relevant content
        search_results = await vector_store.similarity_search("maintenance", k=3)
        
        # Step 4: Generate response with LLM
        if search_results:
            llm_response = await llm_handler.generate_response(
                query="What maintenance procedures are described?",
                context_chunks=search_results,
                response_format="json"
            )
            
            assert llm_response is not None
            assert "response" in llm_response
            
            # Step 5: Post-process the response
            cleaned_response = post_processor.clean_response_data(llm_response)
            assert cleaned_response is not None
            
            # Step 6: Export results
            export_path = post_processor.json_to_csv([cleaned_response], "pipeline_test")
            assert os.path.exists(export_path)
            
            # Clean up
            os.remove(export_path)
        
        # Clean up: Delete the test document
        await vector_store.delete_document(doc_id)

def test_sample_documents():
    """Test that sample documents exist and are valid PDFs"""
    sample_dir = Path(__file__).parent.parent / "sample"
    
    assert sample_dir.exists(), "Sample directory does not exist"
    
    pdf_files = list(sample_dir.glob("*.pdf"))
    assert len(pdf_files) > 0, "No PDF files found in sample directory"
    
    for pdf_file in pdf_files:
        assert pdf_file.stat().st_size > 0, f"PDF file {pdf_file.name} is empty"
        
        # Basic PDF validation
        with open(pdf_file, 'rb') as f:
            header = f.read(4)
            assert header == b'%PDF', f"File {pdf_file.name} is not a valid PDF"

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
