"""
LLM Handler for Output Generation Pipeline
Manages interactions with various LLM providers and prompt engineering
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import asyncio

from openai import AsyncOpenAI
import anthropic
from mistralai.client import MistralClient
try:
    from mistralai.models.chat_completion import ChatMessage
except ImportError:
    # Fallback for newer versions of mistralai
    try:
        from mistralai import ChatMessage
    except ImportError:
        ChatMessage = None

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    MISTRAL = "mistral"

class LLMHandler:
    """Handles LLM interactions for query processing and response generation"""
    
    def __init__(self, 
                 default_provider: LLMProvider = LLMProvider.OPENAI,
                 default_model: str = "gpt-4o-mini"):
        """
        Initialize LLM handler
        
        Args:
            default_provider: Default LLM provider to use
            default_model: Default model name
        """
        self.default_provider = default_provider
        self.default_model = default_model
        
        # Initialize clients
        self.clients = {}
        self._init_clients()
        
        logger.info(f"LLM<PERSON>andler initialized with {default_provider.value}")
    
    def _init_clients(self):
        """Initialize LLM clients"""
        # OpenAI
        if os.getenv("OPENAI_API_KEY"):
            self.openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            self.clients[LLMProvider.OPENAI] = self.openai_client
            logger.info("OpenAI client initialized")
        
        # Anthropic
        if os.getenv("ANTHROPIC_API_KEY"):
            self.clients[LLMProvider.ANTHROPIC] = anthropic.Anthropic(
                api_key=os.getenv("ANTHROPIC_API_KEY")
            )
            logger.info("Anthropic client initialized")
        
        # Mistral
        if os.getenv("MISTRAL_API_KEY"):
            self.clients[LLMProvider.MISTRAL] = MistralClient(
                api_key=os.getenv("MISTRAL_API_KEY")
            )
            logger.info("Mistral client initialized")
    
    async def generate_response(self, 
                              query: str,
                              context_chunks: List[Dict[str, Any]],
                              response_format: str = "json",
                              provider: Optional[LLMProvider] = None,
                              model: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate response using LLM with enriched context
        
        Args:
            query: User query
            context_chunks: Relevant document chunks from vector search
            response_format: Format of response (json, text, markdown)
            provider: LLM provider to use
            model: Model name to use
            
        Returns:
            Generated response with metadata
        """
        try:
            provider = provider or self.default_provider
            model = model or self.default_model
            
            # Build enriched prompt
            prompt = self._build_enriched_prompt(query, context_chunks, response_format)
            
            # Generate response based on provider
            if provider == LLMProvider.OPENAI:
                response = await self._generate_openai_response(prompt, model)
            elif provider == LLMProvider.ANTHROPIC:
                response = await self._generate_anthropic_response(prompt, model)
            elif provider == LLMProvider.MISTRAL:
                response = await self._generate_mistral_response(prompt, model)
            else:
                raise ValueError(f"Unsupported provider: {provider}")
            
            # Parse and format response
            formatted_response = self._format_response(response, response_format)
            
            return {
                "response": formatted_response,
                "query": query,
                "provider": provider.value,
                "model": model,
                "context_chunks_used": len(context_chunks),
                "sources": [chunk.get("filename", "Unknown") for chunk in context_chunks],
                "metadata": {
                    "response_format": response_format,
                    "context_quality": self._assess_context_quality(context_chunks),
                    "confidence_score": self._calculate_confidence_score(context_chunks)
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                "response": {"error": str(e)},
                "query": query,
                "provider": provider.value if provider else "unknown",
                "model": model or "unknown",
                "context_chunks_used": 0,
                "sources": [],
                "metadata": {"error": True}
            }
    
    def _build_enriched_prompt(self, 
                             query: str, 
                             context_chunks: List[Dict[str, Any]], 
                             response_format: str) -> str:
        """Build enriched prompt with context"""
        
        # Format context chunks
        context_text = ""
        for i, chunk in enumerate(context_chunks, 1):
            source_info = f"Source {i}: {chunk.get('filename', 'Unknown')} (Page {chunk.get('page_number', 'N/A')})"
            content = chunk.get('content', '')[:1000]  # Limit chunk size
            context_text += f"\n{source_info}\n{content}\n{'='*50}\n"
        
        # Build prompt based on response format
        if response_format == "json":
            format_instruction = """
Please provide your response in valid JSON format with the following structure:
{
    "answer": "Your detailed answer here",
    "key_points": ["Point 1", "Point 2", "Point 3"],
    "sources_used": ["Source 1", "Source 2"],
    "confidence": "high/medium/low",
    "additional_info": "Any additional relevant information"
}
"""
        elif response_format == "markdown":
            format_instruction = """
Please provide your response in well-formatted Markdown with:
- Clear headings and subheadings
- Bullet points for key information
- Code blocks for technical details if applicable
- Source citations at the end
"""
        else:
            format_instruction = "Please provide a clear, comprehensive text response."
        
        prompt = f"""You are an expert document analysis assistant. You have been provided with relevant document excerpts to answer the user's question.

CONTEXT DOCUMENTS:
{context_text}

USER QUESTION:
{query}

INSTRUCTIONS:
1. Analyze the provided context carefully
2. Answer the question based ONLY on the information provided in the context
3. If the context doesn't contain enough information, clearly state this
4. Cite specific sources when making claims
5. Be precise and factual

{format_instruction}

Please provide your response now:"""
        
        return prompt
    
    async def _generate_openai_response(self, prompt: str, model: str) -> str:
        """Generate response using OpenAI"""
        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a helpful document analysis assistant."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise
    
    async def _generate_anthropic_response(self, prompt: str, model: str = "claude-3-sonnet-20240229") -> str:
        """Generate response using Anthropic Claude"""
        try:
            client = self.clients[LLMProvider.ANTHROPIC]
            
            response = client.messages.create(
                model=model,
                max_tokens=2000,
                temperature=0.1,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Anthropic API error: {str(e)}")
            raise
    
    async def _generate_mistral_response(self, prompt: str, model: str = "mistral-medium") -> str:
        """Generate response using Mistral"""
        try:
            client = self.clients[LLMProvider.MISTRAL]
            
            response = client.chat(
                model=model,
                messages=[
                    ChatMessage(role="user", content=prompt)
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Mistral API error: {str(e)}")
            raise
    
    def _format_response(self, response: str, response_format: str) -> Union[Dict, str]:
        """Format response based on requested format"""
        if response_format == "json":
            try:
                # Try to parse as JSON
                return json.loads(response)
            except json.JSONDecodeError:
                # If parsing fails, wrap in JSON structure
                return {
                    "answer": response,
                    "key_points": [],
                    "sources_used": [],
                    "confidence": "medium",
                    "additional_info": "Response was not in valid JSON format"
                }
        else:
            return response
    
    def _assess_context_quality(self, context_chunks: List[Dict[str, Any]]) -> str:
        """Assess the quality of context chunks"""
        if not context_chunks:
            return "no_context"
        
        avg_similarity = sum(chunk.get("similarity_score", 0) for chunk in context_chunks) / len(context_chunks)
        
        if avg_similarity > 0.8:
            return "high"
        elif avg_similarity > 0.6:
            return "medium"
        else:
            return "low"
    
    def _calculate_confidence_score(self, context_chunks: List[Dict[str, Any]]) -> float:
        """Calculate confidence score based on context quality"""
        if not context_chunks:
            return 0.0
        
        # Factors affecting confidence:
        # 1. Number of relevant chunks
        # 2. Average similarity score
        # 3. Diversity of sources
        
        num_chunks = len(context_chunks)
        avg_similarity = sum(chunk.get("similarity_score", 0) for chunk in context_chunks) / num_chunks
        unique_sources = len(set(chunk.get("filename", "") for chunk in context_chunks))
        
        # Normalize factors
        chunk_factor = min(num_chunks / 5, 1.0)  # Optimal around 5 chunks
        similarity_factor = avg_similarity
        diversity_factor = min(unique_sources / 3, 1.0)  # Optimal around 3 different sources
        
        confidence = (chunk_factor * 0.3 + similarity_factor * 0.5 + diversity_factor * 0.2)
        
        return round(confidence, 2)
    
    async def summarize_document(self, 
                               document_content: str,
                               max_length: int = 500,
                               provider: Optional[LLMProvider] = None) -> str:
        """Generate a summary of document content"""
        try:
            provider = provider or self.default_provider
            
            prompt = f"""Please provide a concise summary of the following document content in approximately {max_length} characters:

DOCUMENT CONTENT:
{document_content[:4000]}  # Limit input size

INSTRUCTIONS:
1. Capture the main topics and key information
2. Keep the summary factual and objective
3. Highlight any important technical details or specifications
4. Make it readable and well-structured

Summary:"""
            
            if provider == LLMProvider.OPENAI:
                response = await self._generate_openai_response(prompt, "gpt-3.5-turbo")
            elif provider == LLMProvider.ANTHROPIC:
                response = await self._generate_anthropic_response(prompt, "claude-3-haiku-20240307")
            else:
                response = await self._generate_mistral_response(prompt, "mistral-small")
            
            return response[:max_length] if len(response) > max_length else response
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return "Summary could not be generated due to an error."
    
    async def extract_key_information(self, 
                                    document_content: str,
                                    extraction_type: str = "general") -> Dict[str, Any]:
        """Extract structured information from document content"""
        try:
            extraction_prompts = {
                "general": "Extract key information including main topics, important facts, and any specifications.",
                "technical": "Extract technical specifications, procedures, requirements, and safety information.",
                "maintenance": "Extract maintenance procedures, schedules, requirements, and safety protocols.",
                "parts": "Extract parts lists, component specifications, and assembly information."
            }
            
            prompt = f"""Analyze the following document and {extraction_prompts.get(extraction_type, extraction_prompts['general'])}

DOCUMENT CONTENT:
{document_content[:4000]}

Please provide the extracted information in JSON format with appropriate categories and details.

Response:"""
            
            response = await self._generate_openai_response(prompt, "gpt-4o-mini")
            
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                return {"extracted_info": response, "format": "text"}
                
        except Exception as e:
            logger.error(f"Error extracting information: {str(e)}")
            return {"error": str(e)}
