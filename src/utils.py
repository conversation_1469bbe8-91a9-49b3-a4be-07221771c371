"""
Utility functions for the RAG pipeline
"""

import os
import logging
import hashlib
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import time
from functools import wraps

logger = logging.getLogger(__name__)

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )

def validate_environment():
    """Validate required environment variables"""
    required_vars = [
        "SUPABASE_URL",
        "SUPABASE_KEY"
    ]
    
    optional_vars = [
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY", 
        "MISTRAL_API_KEY"
    ]
    
    missing_required = []
    missing_optional = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
    
    for var in optional_vars:
        if not os.getenv(var):
            missing_optional.append(var)
    
    if missing_required:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_required)}")
    
    if missing_optional:
        logger.warning(f"Missing optional environment variables: {', '.join(missing_optional)}")
    
    logger.info("Environment validation completed")

def calculate_file_hash(file_path: str) -> str:
    """Calculate SHA-256 hash of a file"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating hash for {file_path}: {str(e)}")
        return ""

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    import re
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove excessive dots and spaces
    sanitized = re.sub(r'\.+', '.', sanitized)
    sanitized = re.sub(r'\s+', ' ', sanitized).strip()
    # Limit length
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        sanitized = name[:255-len(ext)] + ext
    
    return sanitized

def retry_with_backoff(max_retries: int = 3, backoff_factor: float = 1.0):
    """Decorator for retrying functions with exponential backoff"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        wait_time = backoff_factor * (2 ** attempt)
                        logger.warning(f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {wait_time}s...")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"All {max_retries} attempts failed. Last error: {str(e)}")
            
            raise last_exception
        
        return wrapper
    return decorator

def chunk_text(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
    """Split text into overlapping chunks"""
    if not text:
        return []
    
    words = text.split()
    chunks = []
    
    for i in range(0, len(words), chunk_size - overlap):
        chunk_words = words[i:i + chunk_size]
        chunk_text = ' '.join(chunk_words)
        
        chunks.append({
            "text": chunk_text,
            "start_word": i,
            "end_word": min(i + chunk_size, len(words)),
            "word_count": len(chunk_words),
            "char_count": len(chunk_text)
        })
    
    return chunks

def extract_text_statistics(text: str) -> Dict[str, Any]:
    """Extract basic statistics from text"""
    if not text:
        return {
            "char_count": 0,
            "word_count": 0,
            "sentence_count": 0,
            "paragraph_count": 0,
            "avg_word_length": 0,
            "avg_sentence_length": 0
        }
    
    import re
    
    char_count = len(text)
    words = text.split()
    word_count = len(words)
    
    # Count sentences (simple approach)
    sentences = re.split(r'[.!?]+', text)
    sentence_count = len([s for s in sentences if s.strip()])
    
    # Count paragraphs
    paragraphs = text.split('\n\n')
    paragraph_count = len([p for p in paragraphs if p.strip()])
    
    # Calculate averages
    avg_word_length = sum(len(word) for word in words) / word_count if word_count > 0 else 0
    avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
    
    return {
        "char_count": char_count,
        "word_count": word_count,
        "sentence_count": sentence_count,
        "paragraph_count": paragraph_count,
        "avg_word_length": round(avg_word_length, 2),
        "avg_sentence_length": round(avg_sentence_length, 2)
    }

def load_config(config_path: str = "config.json") -> Dict[str, Any]:
    """Load configuration from JSON file"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        else:
            logger.warning(f"Config file {config_path} not found. Using defaults.")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return {}

def save_config(config: Dict[str, Any], config_path: str = "config.json"):
    """Save configuration to JSON file"""
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"Configuration saved to {config_path}")
    except Exception as e:
        logger.error(f"Error saving config: {str(e)}")

def create_directory_structure(base_path: str):
    """Create necessary directory structure"""
    directories = [
        "outputs",
        "logs",
        "temp",
        "cache"
    ]
    
    base = Path(base_path)
    for directory in directories:
        dir_path = base / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Created directory: {dir_path}")

def cleanup_temp_files(temp_dir: str = "temp", max_age_hours: int = 24):
    """Clean up temporary files older than specified age"""
    try:
        temp_path = Path(temp_dir)
        if not temp_path.exists():
            return
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        removed_count = 0
        for file_path in temp_path.iterdir():
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    file_path.unlink()
                    removed_count += 1
        
        logger.info(f"Cleaned up {removed_count} temporary files")
        
    except Exception as e:
        logger.error(f"Error cleaning up temp files: {str(e)}")

def validate_pdf_content(file_path: str) -> Dict[str, Any]:
    """Validate PDF file and extract basic info"""
    try:
        from PyPDF2 import PdfReader
        
        path = Path(file_path)
        if not path.exists():
            return {"valid": False, "error": "File does not exist"}
        
        if path.suffix.lower() != '.pdf':
            return {"valid": False, "error": "Not a PDF file"}
        
        # Try to read the PDF
        reader = PdfReader(file_path)
        
        return {
            "valid": True,
            "pages": len(reader.pages),
            "size_bytes": path.stat().st_size,
            "size_formatted": format_file_size(path.stat().st_size),
            "encrypted": reader.is_encrypted,
            "metadata": reader.metadata._get_object() if reader.metadata else {}
        }
        
    except Exception as e:
        return {"valid": False, "error": str(e)}

def measure_performance(func):
    """Decorator to measure function execution time"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"{func.__name__} executed in {execution_time:.2f} seconds")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.2f} seconds: {str(e)}")
            raise
    
    return wrapper

class ProgressTracker:
    """Simple progress tracking utility"""
    
    def __init__(self, total_steps: int, description: str = "Processing"):
        self.total_steps = total_steps
        self.current_step = 0
        self.description = description
        self.start_time = time.time()
    
    def update(self, step: int = None, message: str = None):
        """Update progress"""
        if step is not None:
            self.current_step = step
        else:
            self.current_step += 1
        
        progress = (self.current_step / self.total_steps) * 100
        elapsed_time = time.time() - self.start_time
        
        if self.current_step > 0:
            eta = (elapsed_time / self.current_step) * (self.total_steps - self.current_step)
            eta_str = f"ETA: {eta:.1f}s"
        else:
            eta_str = "ETA: --"
        
        status = f"{self.description}: {self.current_step}/{self.total_steps} ({progress:.1f}%) - {eta_str}"
        
        if message:
            status += f" - {message}"
        
        logger.info(status)
        return status
    
    def complete(self):
        """Mark as complete"""
        total_time = time.time() - self.start_time
        logger.info(f"{self.description} completed in {total_time:.2f} seconds")

# Configuration defaults
DEFAULT_CONFIG = {
    "document_processing": {
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "extract_images": True,
        "max_file_size_mb": 50
    },
    "vector_store": {
        "embedding_model": "text-embedding-3-small",
        "embedding_dimension": 1536,
        "similarity_threshold": 0.7
    },
    "llm": {
        "default_provider": "openai",
        "default_model": "gpt-4o-mini",
        "max_tokens": 2000,
        "temperature": 0.1
    },
    "search": {
        "default_k": 5,
        "max_k": 20,
        "hybrid_weights": {
            "vector": 0.7,
            "keyword": 0.3
        }
    }
}
