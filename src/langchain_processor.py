"""
LangChain-based Document Processing Pipeline
Advanced RAG implementation using LangChain and LlamaIndex
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import asyncio

# LangChain imports
from langchain_community.document_loaders import PyPDFLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_anthropic import <PERSON>t<PERSON>nthropic
from langchain_community.vectorstores import SupabaseVectorStore
from langchain.chains import RetrievalQA, ConversationalRetrievalChain
from langchain.memory import ConversationBufferMemory
from langchain_core.documents import Document
from langchain_core.callbacks import Async<PERSON>allbackHandler

# LlamaIndex imports
from llama_index.core import VectorStoreIndex, Settings
from llama_index.core.node_parser import SimpleNodeParser
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.llms.anthropic import Anthropic as LlamaAnthropic
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.supabase import SupabaseVectorStore as LlamaSupabaseVectorStore

# Docling for advanced PDF processing
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.document_converter import PdfFormatOption

# Supabase
from supabase import create_client, Client

logger = logging.getLogger(__name__)

class AdvancedRAGProcessor:
    """Advanced RAG processor using LangChain and LlamaIndex"""
    
    def __init__(self):
        """Initialize the advanced RAG processor"""
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        
        # Initialize Supabase client
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        
        # Initialize embeddings
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=self.openai_api_key,
            model="text-embedding-3-small"
        )
        
        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        
        # Initialize Docling converter for advanced PDF processing
        self.docling_converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(
                    pipeline_options=PdfPipelineOptions(
                        do_ocr=True,
                        do_table_structure=True,
                        table_structure_options={
                            "do_cell_matching": True,
                        }
                    )
                )
            }
        )
        
        # Initialize vector store
        self.vector_store = None
        self.llama_index = None
        
        # Initialize LLMs
        self.chat_llm = None
        self.llama_llm = None
        
        # Initialize chains
        self.qa_chain = None
        self.conversational_chain = None
        
        # Memory for conversations
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        logger.info("Advanced RAG Processor initialized")
    
    def _setup_llms(self, provider: str = "openai"):
        """Setup LLM providers"""
        if provider.lower() == "openai":
            self.chat_llm = ChatOpenAI(
                openai_api_key=self.openai_api_key,
                model_name="gpt-4o-mini",
                temperature=0.1
            )
            self.llama_llm = LlamaOpenAI(
                api_key=self.openai_api_key,
                model="gpt-4o-mini",
                temperature=0.1
            )
        elif provider.lower() == "anthropic":
            self.chat_llm = ChatAnthropic(
                anthropic_api_key=self.anthropic_api_key,
                model="claude-3-sonnet-20240229",
                temperature=0.1
            )
            self.llama_llm = LlamaAnthropic(
                api_key=self.anthropic_api_key,
                model="claude-3-sonnet-20240229",
                temperature=0.1
            )
    
    async def process_pdf_with_docling(self, file_path: str) -> List[Document]:
        """Process PDF using Docling for advanced extraction"""
        try:
            logger.info(f"Processing PDF with Docling: {file_path}")
            
            # Convert document using Docling
            result = self.docling_converter.convert(file_path)
            
            # Extract markdown content
            markdown_content = result.document.export_to_markdown()
            
            # Create metadata
            metadata = {
                "source": file_path,
                "filename": Path(file_path).name,
                "total_pages": len(result.document.pages),
                "processing_method": "docling"
            }
            
            # Process each page
            documents = []
            for page_num, page in enumerate(result.document.pages, 1):
                page_content = page.export_to_markdown() if hasattr(page, 'export_to_markdown') else ""
                
                if page_content.strip():
                    page_metadata = {
                        **metadata,
                        "page_number": page_num,
                        "page_type": "content"
                    }
                    
                    documents.append(Document(
                        page_content=page_content,
                        metadata=page_metadata
                    ))
            
            # Also add full document
            if markdown_content.strip():
                full_doc_metadata = {
                    **metadata,
                    "page_number": 0,
                    "page_type": "full_document"
                }
                
                documents.append(Document(
                    page_content=markdown_content,
                    metadata=full_doc_metadata
                ))
            
            logger.info(f"Extracted {len(documents)} document chunks from {file_path}")
            return documents
            
        except Exception as e:
            logger.error(f"Error processing PDF with Docling: {str(e)}")
            # Fallback to standard PDF loader
            return await self._fallback_pdf_processing(file_path)
    
    async def _fallback_pdf_processing(self, file_path: str) -> List[Document]:
        """Fallback PDF processing using LangChain's PyPDFLoader"""
        try:
            loader = PyPDFLoader(file_path)
            documents = loader.load()
            
            # Add processing method to metadata
            for doc in documents:
                doc.metadata["processing_method"] = "pypdf_fallback"
                doc.metadata["filename"] = Path(file_path).name
            
            return documents
            
        except Exception as e:
            logger.error(f"Fallback PDF processing failed: {str(e)}")
            return []
    
    async def create_vector_store(self, documents: List[Document]) -> None:
        """Create vector store from documents"""
        try:
            # Split documents into chunks
            texts = self.text_splitter.split_documents(documents)
            
            logger.info(f"Split {len(documents)} documents into {len(texts)} chunks")
            
            # Create LangChain vector store
            logger.info(f"🗄️ Creating LangChain vector store with {len(texts)} documents...")
            logger.info(f"📊 Supabase client status: {self.supabase is not None}")
            logger.info(f"🔗 Embeddings status: {self.embeddings is not None}")

            self.vector_store = SupabaseVectorStore.from_documents(
                texts,
                self.embeddings,
                client=self.supabase,
                table_name="document_pages",
                query_name="match_documents",
                chunk_size=1000
            )

            logger.info("✅ LangChain vector store created successfully")
            
            # For now, skip LlamaIndex vector store creation to avoid PostgreSQL connection issues
            # We'll use LangChain's vector store for both frameworks

            # Configure LlamaIndex settings
            Settings.llm = self.llama_llm
            Settings.embed_model = OpenAIEmbedding(api_key=self.openai_api_key)

            # Convert LangChain documents to LlamaIndex format and create simple index
            from llama_index.core import Document as LlamaDocument
            llama_docs = [
                LlamaDocument(
                    text=doc.page_content,
                    metadata=doc.metadata
                ) for doc in texts
            ]

            # Create simple in-memory LlamaIndex for now
            self.llama_index = VectorStoreIndex.from_documents(llama_docs)
            
            logger.info("Vector stores created successfully")
            
        except Exception as e:
            logger.error(f"Error creating vector store: {str(e)}")
            raise
    
    def setup_chains(self, provider: str = "openai"):
        """Setup QA and conversational chains"""
        logger.info(f"🔗 Setting up chains with provider: {provider}")
        logger.info(f"🗄️ Vector store status: {self.vector_store is not None}")

        self._setup_llms(provider)
        logger.info(f"🤖 LLMs setup complete")

        if not self.vector_store:
            error_msg = "Vector store not initialized. Call create_vector_store first."
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)

        # Create retriever
        logger.info("🔍 Creating retriever...")
        retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 5}
        )
        logger.info("✅ Retriever created")

        # Test retriever
        try:
            logger.info("🧪 Testing retriever with sample query...")
            test_docs = retriever.get_relevant_documents("test")
            logger.info(f"📚 Retriever test: found {len(test_docs)} documents")
        except Exception as e:
            logger.warning(f"⚠️ Retriever test failed: {str(e)}")

        # Setup QA chain
        logger.info("🔗 Creating QA chain...")
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.chat_llm,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=True
        )
        logger.info("✅ QA chain created")

        # Setup conversational chain
        logger.info("💬 Creating conversational chain...")
        self.conversational_chain = ConversationalRetrievalChain.from_llm(
            llm=self.chat_llm,
            retriever=retriever,
            memory=self.memory,
            return_source_documents=True
        )
        logger.info("✅ Conversational chain created")

        logger.info(f"🎉 All chains setup complete with {provider} provider")
    
    async def query_documents(self, query: str, use_conversation: bool = False) -> Dict[str, Any]:
        """Query documents using LangChain"""
        try:
            logger.info(f"🔍 Starting LangChain query: '{query}'")
            logger.info(f"📊 Vector store status: {self.vector_store is not None}")
            logger.info(f"🔗 QA chain status: {self.qa_chain is not None}")
            logger.info(f"💬 Conversational chain status: {self.conversational_chain is not None}")
            logger.info(f"🎯 Use conversation: {use_conversation}")

            if use_conversation and self.conversational_chain:
                logger.info("🔄 Using conversational chain")
                result = await self.conversational_chain.ainvoke({"question": query})
            elif self.qa_chain:
                logger.info("🔄 Using QA chain")
                result = await self.qa_chain.ainvoke({"query": query})
            else:
                error_msg = "No chains initialized. Call setup_chains first."
                logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)

            logger.info(f"📝 Raw result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            logger.info(f"📄 Source documents count: {len(result.get('source_documents', []))}")

            # Format response
            response = {
                "answer": result.get("answer", ""),
                "source_documents": [
                    {
                        "content": doc.page_content,
                        "metadata": doc.metadata,
                        "source": doc.metadata.get("source", ""),
                        "page": doc.metadata.get("page_number", 0)
                    }
                    for doc in result.get("source_documents", [])
                ],
                "query": query,
                "method": "langchain"
            }

            logger.info(f"✅ LangChain query completed. Answer length: {len(response['answer'])}")
            logger.info(f"📚 Sources found: {len(response['source_documents'])}")

            return response

        except Exception as e:
            logger.error(f"❌ Error querying documents: {str(e)}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return {
                "answer": f"Error processing query: {str(e)}",
                "source_documents": [],
                "query": query,
                "error": True
            }
    
    async def query_with_llama_index(self, query: str) -> Dict[str, Any]:
        """Query documents using LlamaIndex"""
        try:
            logger.info(f"🦙 Starting LlamaIndex query: '{query}'")
            logger.info(f"📊 LlamaIndex status: {self.llama_index is not None}")

            if not self.llama_index:
                error_msg = "LlamaIndex not initialized"
                logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)

            logger.info(f"📚 LlamaIndex document count: {len(self.llama_index.docstore.docs) if hasattr(self.llama_index, 'docstore') else 'Unknown'}")

            # Create query engine
            logger.info("🔧 Creating query engine...")
            query_engine = self.llama_index.as_query_engine(
                response_mode="compact",
                similarity_top_k=5
            )

            # Execute query (LlamaIndex doesn't have aquery in newer versions)
            logger.info("🔄 Executing LlamaIndex query...")
            response = query_engine.query(query)

            logger.info(f"📝 LlamaIndex response type: {type(response)}")
            logger.info(f"📄 Response has source_nodes: {hasattr(response, 'source_nodes')}")

            if hasattr(response, 'source_nodes'):
                logger.info(f"📚 Source nodes count: {len(response.source_nodes)}")

            # Format response
            formatted_response = {
                "answer": str(response),
                "source_nodes": [
                    {
                        "content": node.node.text,
                        "metadata": node.node.metadata,
                        "score": node.score if hasattr(node, 'score') else 0.0
                    }
                    for node in (response.source_nodes if hasattr(response, 'source_nodes') else [])
                ],
                "query": query,
                "method": "llama_index"
            }

            logger.info(f"✅ LlamaIndex query completed. Answer length: {len(formatted_response['answer'])}")
            logger.info(f"📚 Sources found: {len(formatted_response['source_nodes'])}")

            return formatted_response

        except Exception as e:
            logger.error(f"❌ Error querying with LlamaIndex: {str(e)}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return {
                "answer": f"Error processing query: {str(e)}",
                "source_nodes": [],
                "query": query,
                "error": True
            }
    
    async def hybrid_query(self, query: str) -> Dict[str, Any]:
        """Perform hybrid query using both LangChain and LlamaIndex"""
        try:
            # Get results from both systems
            langchain_result = await self.query_documents(query)
            llama_result = await self.query_with_llama_index(query)
            
            # Combine results
            hybrid_response = {
                "query": query,
                "langchain_answer": langchain_result.get("answer", ""),
                "llama_index_answer": llama_result.get("answer", ""),
                "combined_sources": {
                    "langchain_sources": langchain_result.get("source_documents", []),
                    "llama_index_sources": llama_result.get("source_nodes", [])
                },
                "method": "hybrid"
            }
            
            # Create a combined answer (simple approach - could be more sophisticated)
            if not langchain_result.get("error") and not llama_result.get("error"):
                hybrid_response["combined_answer"] = f"""
**LangChain Response:**
{langchain_result.get('answer', '')}

**LlamaIndex Response:**
{llama_result.get('answer', '')}
"""
            else:
                hybrid_response["combined_answer"] = langchain_result.get("answer", "") or llama_result.get("answer", "")
            
            return hybrid_response
            
        except Exception as e:
            logger.error(f"Error in hybrid query: {str(e)}")
            return {
                "query": query,
                "combined_answer": f"Error processing hybrid query: {str(e)}",
                "error": True
            }
    
    async def process_multiple_pdfs(self, file_paths: List[str]) -> Dict[str, Any]:
        """Process multiple PDF files"""
        all_documents = []
        processing_results = {
            "processed": [],
            "failed": [],
            "total_documents": 0
        }
        
        for file_path in file_paths:
            try:
                documents = await self.process_pdf_with_docling(file_path)
                if documents:
                    all_documents.extend(documents)
                    processing_results["processed"].append({
                        "file": file_path,
                        "documents_count": len(documents)
                    })
                else:
                    processing_results["failed"].append({
                        "file": file_path,
                        "error": "No documents extracted"
                    })
            except Exception as e:
                processing_results["failed"].append({
                    "file": file_path,
                    "error": str(e)
                })
        
        processing_results["total_documents"] = len(all_documents)
        
        # Create vector store if we have documents
        if all_documents:
            await self.create_vector_store(all_documents)
        
        return processing_results
