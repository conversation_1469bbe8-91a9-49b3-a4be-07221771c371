"""
Advanced Table Extraction System using Docling
Specialized for extracting complete tables as CSV data with image cropping
"""

import os
import logging
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
import csv
from io import StringIO
import fitz  # PyMuPDF for PDF image extraction
from PIL import Image, ImageDraw, ImageFilter
import base64
from io import BytesIO
import cv2
import numpy as np

from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.datamodel.document import ConversionResult

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedTableExtractor:
    """Advanced table extraction system using Docling"""
    
    def __init__(self):
        """Initialize the table extractor"""
        
        # Configure Docling for optimal table extraction
        self.docling_converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(
                    pipeline_options=PdfPipelineOptions(
                        do_ocr=True,
                        do_table_structure=True,
                        table_structure_options={
                            "do_cell_matching": True,
                            "mode": "accurate"  # Use accurate mode for better table detection
                        }
                    )
                )
            }
        )
        
        logger.info("Advanced Table Extractor initialized")
    
    async def extract_tables_from_pdf(self, file_path: str, output_dir: str = "output") -> Dict[str, Any]:
        """
        Extract all tables from PDF as structured data with image cropping

        Args:
            file_path: Path to PDF file
            output_dir: Directory to save extracted images and CSV files

        Returns:
            Dictionary containing extracted tables and metadata
        """
        try:
            logger.info(f"🔍 Extracting tables from: {Path(file_path).name}")

            # Create output directory
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)

            # Convert document using Docling
            result = self.docling_converter.convert(file_path)

            # Extract tables from the document
            tables_data = self._extract_tables_from_result(result)

            # Generate CSV data and crop images for each table
            csv_tables = []
            for i, table_data in enumerate(tables_data):
                csv_content = self._convert_table_to_csv(table_data, i)

                # Crop table image using enhanced detection
                image_path = None
                table_bbox = await self._detect_table_bbox(
                    file_path,
                    table_data,
                    table_data.get("page_number", 1)
                )

                if table_bbox:
                    table_data["metadata"]["bbox"] = table_bbox
                    image_path = await self._crop_table_image(
                        file_path,
                        table_bbox,
                        table_data.get("page_number", 1),
                        output_path,
                        f"table_{i+1}"
                    )
                else:
                    logger.warning(f"⚠️ Could not detect bbox for table {i+1}, skipping image crop")

                csv_tables.append({
                    "table_id": i + 1,
                    "csv_content": csv_content,
                    "metadata": table_data.get("metadata", {}),
                    "page_number": table_data.get("page_number", 0),
                    "image_path": image_path,
                    "bbox": table_data.get("metadata", {}).get("bbox")
                })

            # Extract spare parts specifically
            spare_parts = self._extract_spare_parts_from_tables(csv_tables)

            return {
                "filename": Path(file_path).name,
                "total_tables": len(csv_tables),
                "tables": csv_tables,
                "spare_parts": spare_parts,
                "processing_status": "success",
                "output_directory": str(output_path)
            }

        except Exception as e:
            logger.error(f"❌ Error extracting tables: {str(e)}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return {
                "filename": Path(file_path).name,
                "total_tables": 0,
                "tables": [],
                "spare_parts": [],
                "processing_status": "error",
                "error": str(e)
            }
    
    def _extract_tables_from_result(self, result: ConversionResult) -> List[Dict[str, Any]]:
        """Extract table data from Docling conversion result"""
        tables_data = []
        
        try:
            # Check if document has tables
            if hasattr(result.document, 'tables') and result.document.tables:
                logger.info(f"📊 Found {len(result.document.tables)} tables in document")
                
                for i, table in enumerate(result.document.tables):
                    table_data = self._process_docling_table(table, i)
                    if table_data:
                        tables_data.append(table_data)
            
            # Also check pages for tables
            if hasattr(result.document, 'pages') and result.document.pages:
                for page_num, page in enumerate(result.document.pages, 1):
                    if hasattr(page, 'tables') and page.tables:
                        logger.info(f"📄 Found {len(page.tables)} tables on page {page_num}")
                        
                        for i, table in enumerate(page.tables):
                            table_data = self._process_docling_table(table, i, page_num)
                            if table_data:
                                tables_data.append(table_data)
            
            # Fallback: Extract tables from markdown if direct table access fails
            if not tables_data:
                logger.info("🔄 Attempting to extract tables from markdown...")
                markdown_content = result.document.export_to_markdown()
                tables_data = self._extract_tables_from_markdown(markdown_content)
            
        except Exception as e:
            logger.error(f"❌ Error processing tables: {str(e)}")
        
        logger.info(f"✅ Extracted {len(tables_data)} tables total")
        return tables_data
    
    def _process_docling_table(self, table: Any, table_index: int, page_number: int = 0) -> Optional[Dict[str, Any]]:
        """Process a single Docling table object"""
        try:
            # Extract table data based on Docling's table structure
            table_data = {
                "table_index": table_index,
                "page_number": page_number,
                "metadata": {}
            }

            logger.info(f"🔍 Processing table {table_index}, type: {type(table)}")
            logger.info(f"📋 Table attributes: {[attr for attr in dir(table) if not attr.startswith('_')]}")

            # Try different methods to extract table content
            if hasattr(table, 'data') and table.data:
                table_data["docling_data"] = table.data
                logger.info(f"📊 Found table.data: {type(table.data)}")

                # Try to access table cells from data
                if hasattr(table.data, 'table_cells'):
                    table_data["cells"] = table.data.table_cells
                    logger.info(f"🔲 Found table cells: {len(table.data.table_cells)} cells")

                    # Calculate overall table bbox from cells
                    table_bbox = self._calculate_table_bbox_from_cells(table.data.table_cells)
                    if table_bbox:
                        table_data["metadata"]["bbox"] = table_bbox
                        logger.info(f"📦 Calculated table bbox: {table_bbox}")

            # Try to use Docling's export methods
            if hasattr(table, 'export_to_dataframe'):
                try:
                    df = table.export_to_dataframe()
                    table_data["dataframe"] = df
                    logger.info(f"📊 Exported to DataFrame: {df.shape}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not export to DataFrame: {str(e)}")

            if hasattr(table, 'export_to_markdown'):
                try:
                    table_data["markdown"] = table.export_to_markdown()
                    logger.info(f"📝 Exported to markdown: {len(table_data['markdown'])} chars")
                except Exception as e:
                    logger.warning(f"⚠️ Could not export to markdown: {str(e)}")

            if hasattr(table, 'export_to_html'):
                try:
                    table_data["html"] = table.export_to_html()
                    logger.info(f"🌐 Exported to HTML: {len(table_data['html'])} chars")
                except Exception as e:
                    logger.warning(f"⚠️ Could not export to HTML: {str(e)}")

            # Extract direct table metadata
            if hasattr(table, 'bbox'):
                table_data["metadata"]["bbox"] = table.bbox
                logger.info(f"📦 Direct table bbox: {table.bbox}")

            if hasattr(table, 'confidence'):
                table_data["metadata"]["confidence"] = table.confidence

            return table_data

        except Exception as e:
            logger.warning(f"⚠️ Could not process table {table_index}: {str(e)}")
            import traceback
            logger.warning(f"📋 Traceback: {traceback.format_exc()}")
            return None
    
    def _extract_tables_from_markdown(self, markdown_content: str) -> List[Dict[str, Any]]:
        """Extract tables from markdown content as fallback"""
        tables_data = []
        
        try:
            # Split content by lines
            lines = markdown_content.split('\n')
            current_table = []
            table_index = 0
            in_table = False
            
            for line in lines:
                # Detect table start (markdown table format)
                if '|' in line and line.strip().startswith('|'):
                    if not in_table:
                        in_table = True
                        current_table = []
                    current_table.append(line.strip())
                else:
                    # End of table
                    if in_table and current_table:
                        table_data = {
                            "table_index": table_index,
                            "page_number": 0,
                            "markdown_lines": current_table,
                            "metadata": {"source": "markdown_extraction"}
                        }
                        tables_data.append(table_data)
                        table_index += 1
                        current_table = []
                        in_table = False
            
            # Handle last table if file ends with table
            if in_table and current_table:
                table_data = {
                    "table_index": table_index,
                    "page_number": 0,
                    "markdown_lines": current_table,
                    "metadata": {"source": "markdown_extraction"}
                }
                tables_data.append(table_data)
        
        except Exception as e:
            logger.error(f"❌ Error extracting tables from markdown: {str(e)}")
        
        return tables_data
    
    def _calculate_table_bbox_from_cells(self, cells) -> Optional[Dict[str, float]]:
        """Calculate overall table bounding box from individual cells"""
        try:
            if not cells:
                return None

            min_left = float('inf')
            min_top = float('inf')
            max_right = float('-inf')
            max_bottom = float('-inf')

            for cell in cells:
                if hasattr(cell, 'bbox'):
                    bbox = cell.bbox
                    # Handle different bbox formats
                    if hasattr(bbox, 'l') and hasattr(bbox, 't') and hasattr(bbox, 'r') and hasattr(bbox, 'b'):
                        min_left = min(min_left, bbox.l)
                        min_top = min(min_top, bbox.t)
                        max_right = max(max_right, bbox.r)
                        max_bottom = max(max_bottom, bbox.b)

            if min_left != float('inf'):
                return {
                    "x0": min_left,
                    "y0": min_top,
                    "x1": max_right,
                    "y1": max_bottom
                }

            return None

        except Exception as e:
            logger.warning(f"⚠️ Error calculating table bbox: {str(e)}")
            return None

    def _convert_table_to_csv(self, table_data: Dict[str, Any], table_index: int) -> str:
        """Convert table data to CSV format"""
        try:
            csv_content = ""

            # Priority 1: Use DataFrame if available (best option)
            if "dataframe" in table_data and table_data["dataframe"] is not None:
                logger.info(f"📊 Using DataFrame for table {table_index}")
                df = table_data["dataframe"]
                csv_content = df.to_csv(index=False)
                logger.info(f"✅ DataFrame to CSV: {len(csv_content)} chars")
                return csv_content

            # Priority 2: Use cell-based data
            elif "cells" in table_data and table_data["cells"]:
                logger.info(f"🔲 Using cells for table {table_index}")
                csv_content = self._cells_to_csv(table_data["cells"])

            # Priority 3: Use markdown table format
            elif "markdown" in table_data:
                logger.info(f"📝 Using markdown for table {table_index}")
                lines = table_data["markdown"].split('\n')
                csv_content = self._markdown_table_to_csv(lines)

            # Priority 4: Use HTML (convert to markdown first)
            elif "html" in table_data:
                logger.info(f"🌐 Using HTML for table {table_index}")
                # Simple HTML table parsing
                csv_content = self._html_to_csv(table_data["html"])

            # Fallback: Direct row data
            elif "rows" in table_data and table_data["rows"]:
                logger.info(f"📋 Using rows for table {table_index}")
                csv_content = self._rows_to_csv(table_data["rows"])

            # Last resort: markdown lines
            elif "markdown_lines" in table_data:
                logger.info(f"📄 Using markdown lines for table {table_index}")
                csv_content = self._markdown_table_to_csv(table_data["markdown_lines"])

            else:
                logger.warning(f"⚠️ No suitable data format found for table {table_index}")
                csv_content = f"# No data available for table {table_index}"

            return csv_content

        except Exception as e:
            logger.error(f"❌ Error converting table {table_index} to CSV: {str(e)}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return f"# Error converting table {table_index}: {str(e)}"
    
    def _markdown_table_to_csv(self, markdown_lines: List[str]) -> str:
        """Convert markdown table lines to CSV"""
        try:
            csv_rows = []
            
            for line in markdown_lines:
                if '|' in line:
                    # Remove leading/trailing pipes and split
                    cells = [cell.strip() for cell in line.strip('|').split('|')]
                    # Skip separator lines (contain only - and |)
                    if not all(cell.replace('-', '').replace(' ', '') == '' for cell in cells):
                        csv_rows.append(cells)
            
            if csv_rows:
                # Convert to CSV string
                output = StringIO()
                writer = csv.writer(output)
                writer.writerows(csv_rows)
                return output.getvalue()
            
            return ""
            
        except Exception as e:
            logger.error(f"❌ Error converting markdown to CSV: {str(e)}")
            return ""
    
    def _rows_to_csv(self, rows_data: Any) -> str:
        """Convert row data to CSV"""
        try:
            if isinstance(rows_data, list):
                output = StringIO()
                writer = csv.writer(output)
                
                for row in rows_data:
                    if isinstance(row, list):
                        writer.writerow(row)
                    elif isinstance(row, dict):
                        writer.writerow(row.values())
                    else:
                        writer.writerow([str(row)])
                
                return output.getvalue()
            
            return str(rows_data)
            
        except Exception as e:
            logger.error(f"❌ Error converting rows to CSV: {str(e)}")
            return ""
    
    def _cells_to_csv(self, cells_data: Any) -> str:
        """Convert Docling cell data to CSV format"""
        try:
            if not cells_data:
                return ""

            logger.info(f"🔲 Converting {len(cells_data)} cells to CSV")

            # Create a grid structure from cells
            cell_grid = {}
            max_row = 0
            max_col = 0

            for cell in cells_data:
                if hasattr(cell, 'start_row_offset_idx') and hasattr(cell, 'start_col_offset_idx'):
                    row = cell.start_row_offset_idx
                    col = cell.start_col_offset_idx
                    text = getattr(cell, 'text', '')

                    cell_grid[(row, col)] = text
                    max_row = max(max_row, row)
                    max_col = max(max_col, col)

            if not cell_grid:
                # Fallback: try to extract text from cells
                texts = []
                for cell in cells_data:
                    if hasattr(cell, 'text'):
                        texts.append(str(cell.text))

                if texts:
                    # Create a simple single-row CSV
                    output = StringIO()
                    writer = csv.writer(output)
                    writer.writerow(texts)
                    return output.getvalue()
                else:
                    return f"# Could not extract text from {len(cells_data)} cells"

            # Convert grid to CSV
            output = StringIO()
            writer = csv.writer(output)

            for row in range(max_row + 1):
                row_data = []
                for col in range(max_col + 1):
                    cell_text = cell_grid.get((row, col), '')
                    row_data.append(cell_text)
                writer.writerow(row_data)

            csv_result = output.getvalue()
            logger.info(f"✅ Converted to CSV: {len(csv_result)} chars, {max_row + 1} rows, {max_col + 1} cols")
            return csv_result

        except Exception as e:
            logger.error(f"❌ Error converting cells to CSV: {str(e)}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")

            # Fallback: return raw cell data for debugging
            try:
                return f"# Error converting cells: {str(e)}\n# Raw data: {str(cells_data)[:500]}..."
            except:
                return f"# Error converting cells: {str(e)}"
    
    def _text_to_csv(self, text_data: str) -> str:
        """Convert plain text to CSV if it looks like tabular data"""
        try:
            lines = text_data.split('\n')
            csv_rows = []
            
            for line in lines:
                # Try to detect tabular structure
                if '\t' in line:  # Tab-separated
                    csv_rows.append(line.split('\t'))
                elif '  ' in line:  # Multiple spaces
                    csv_rows.append([cell.strip() for cell in line.split('  ') if cell.strip()])
            
            if csv_rows:
                output = StringIO()
                writer = csv.writer(output)
                writer.writerows(csv_rows)
                return output.getvalue()
            
            return text_data  # Return as-is if no tabular structure detected
            
        except Exception as e:
            logger.error(f"❌ Error converting text to CSV: {str(e)}")
            return text_data

    def _html_to_csv(self, html_content: str) -> str:
        """Convert HTML table to CSV"""
        try:
            # Simple HTML table parsing using pandas
            import pandas as pd
            from io import StringIO

            # Try to read HTML table with pandas
            tables = pd.read_html(StringIO(html_content))

            if tables:
                # Use the first table
                df = tables[0]
                return df.to_csv(index=False)

            return "# No tables found in HTML content"

        except Exception as e:
            logger.warning(f"⚠️ Error converting HTML to CSV: {str(e)}")
            # Fallback: return HTML content as comment
            return f"# HTML content (could not parse): {html_content[:200]}..."

    def _extract_spare_parts_from_tables(self, csv_tables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract spare parts information from CSV tables"""
        spare_parts = []
        
        try:
            for table in csv_tables:
                csv_content = table["csv_content"]
                if not csv_content:
                    continue
                
                # Parse CSV content
                csv_reader = csv.reader(StringIO(csv_content))
                rows = list(csv_reader)
                
                if not rows:
                    continue
                
                # Look for spare parts patterns
                headers = rows[0] if rows else []
                
                # Common spare parts column names
                part_indicators = ['part', 'spare', 'component', 'item', 'description', 'number']
                
                for row_idx, row in enumerate(rows[1:], 1):  # Skip header
                    if len(row) >= len(headers):
                        part_data = {}
                        
                        for col_idx, (header, value) in enumerate(zip(headers, row)):
                            if value and value.strip():
                                part_data[header.lower().strip()] = value.strip()
                        
                        # Check if this row contains spare part information
                        if any(indicator in ' '.join(part_data.keys()).lower() for indicator in part_indicators):
                            part_data.update({
                                "table_id": table["table_id"],
                                "row_number": row_idx,
                                "page_number": table["page_number"]
                            })
                            spare_parts.append(part_data)
            
        except Exception as e:
            logger.error(f"❌ Error extracting spare parts: {str(e)}")
        
        logger.info(f"🔧 Extracted {len(spare_parts)} spare parts")
        return spare_parts
    
    def save_tables_to_files(self, extraction_result: Dict[str, Any], output_dir: str = "extracted_tables") -> List[str]:
        """Save extracted tables to individual CSV files"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            saved_files = []
            filename_base = Path(extraction_result["filename"]).stem
            
            # Save individual tables
            for table in extraction_result["tables"]:
                table_filename = f"{filename_base}_table_{table['table_id']}.csv"
                table_path = output_path / table_filename
                
                with open(table_path, 'w', newline='', encoding='utf-8') as f:
                    f.write(table["csv_content"])
                
                saved_files.append(str(table_path))
                logger.info(f"💾 Saved table to: {table_path}")
            
            # Save combined spare parts
            if extraction_result["spare_parts"]:
                spare_parts_filename = f"{filename_base}_spare_parts.csv"
                spare_parts_path = output_path / spare_parts_filename
                
                # Convert spare parts to DataFrame and save
                df = pd.DataFrame(extraction_result["spare_parts"])
                df.to_csv(spare_parts_path, index=False)
                
                saved_files.append(str(spare_parts_path))
                logger.info(f"🔧 Saved spare parts to: {spare_parts_path}")
            
            return saved_files

        except Exception as e:
            logger.error(f"❌ Error saving tables: {str(e)}")
            return []

    async def _detect_table_bbox(self, pdf_path: str, table_data: Dict[str, Any], page_number: int) -> Optional[Dict[str, float]]:
        """
        Comprehensive table bbox detection using multiple methods

        Args:
            pdf_path: Path to PDF file
            table_data: Table data from Docling
            page_number: Page number (1-based)

        Returns:
            Table bounding box coordinates or None
        """
        try:
            logger.info(f"🔍 Detecting table bbox using multiple methods...")

            # Method 1: Check if Docling provides direct table bbox
            if table_data.get("metadata", {}).get("bbox"):
                bbox = table_data["metadata"]["bbox"]
                logger.info(f"✅ Method 1 - Direct Docling bbox: {bbox}")
                return bbox

            # Method 2: Calculate from cells with padding
            if "cells" in table_data and table_data["cells"]:
                bbox = self._calculate_enhanced_table_bbox_from_cells(table_data["cells"])
                if bbox:
                    logger.info(f"✅ Method 2 - Enhanced cell-based bbox: {bbox}")
                    return bbox

            # Method 3: Visual table detection using image processing
            bbox = await self._detect_table_bbox_visually(pdf_path, page_number, table_data)
            if bbox:
                logger.info(f"✅ Method 3 - Visual detection bbox: {bbox}")
                return bbox

            # Method 4: Content-based detection using text layout
            bbox = await self._detect_table_bbox_from_content(pdf_path, page_number, table_data)
            if bbox:
                logger.info(f"✅ Method 4 - Content-based bbox: {bbox}")
                return bbox

            # Method 5: Try other pages if table might be on different page
            if page_number == 1:  # Only try this for page 1 (0-based indexing issue)
                for try_page in [0, 2]:  # Try page 0 and page 2
                    logger.info(f"🔍 Trying page {try_page} as fallback...")
                    bbox = await self._detect_table_bbox_visually(pdf_path, try_page, table_data)
                    if bbox:
                        logger.info(f"✅ Method 5 - Found table on page {try_page}: {bbox}")
                        return bbox

            logger.warning(f"⚠️ All table bbox detection methods failed")
            return None

        except Exception as e:
            logger.error(f"❌ Error in table bbox detection: {str(e)}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return None

    def _calculate_enhanced_table_bbox_from_cells(self, cells) -> Optional[Dict[str, float]]:
        """Enhanced cell-based bbox calculation with padding and validation"""
        try:
            if not cells:
                return None

            min_left = float('inf')
            min_top = float('inf')
            max_right = float('-inf')
            max_bottom = float('-inf')

            valid_cells = 0

            for cell in cells:
                if hasattr(cell, 'bbox'):
                    bbox = cell.bbox
                    # Handle different bbox formats
                    if hasattr(bbox, 'l') and hasattr(bbox, 't') and hasattr(bbox, 'r') and hasattr(bbox, 'b'):
                        min_left = min(min_left, bbox.l)
                        min_top = min(min_top, bbox.t)
                        max_right = max(max_right, bbox.r)
                        max_bottom = max(max_bottom, bbox.b)
                        valid_cells += 1

            if valid_cells == 0 or min_left == float('inf'):
                return None

            # Add padding around the table (5% of table dimensions)
            width = max_right - min_left
            height = max_bottom - min_top

            padding_x = max(10, width * 0.08)  # At least 10 points padding, 8% of width
            padding_y = max(10, height * 0.08)  # At least 10 points padding, 8% of height

            enhanced_bbox = {
                "x0": max(0, min_left - padding_x),
                "y0": max(0, min_top - padding_y),
                "x1": max_right + padding_x,
                "y1": max_bottom + padding_y
            }

            logger.info(f"📦 Enhanced bbox from {valid_cells} cells with padding: {enhanced_bbox}")
            return enhanced_bbox

        except Exception as e:
            logger.warning(f"⚠️ Error in enhanced cell bbox calculation: {str(e)}")
            return None

    async def _detect_table_bbox_visually(self, pdf_path: str, page_number: int, table_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """
        Detect table bbox using visual/image processing techniques
        This method analyzes the PDF page as an image to detect table boundaries
        """
        try:
            logger.info(f"🖼️ Attempting visual table detection on page {page_number}")

            # Open PDF and render page as image
            pdf_doc = fitz.open(pdf_path)
            page_idx = max(0, page_number - 1)

            if page_idx >= len(pdf_doc):
                return None

            page = pdf_doc[page_idx]

            # Render page as high-resolution image
            zoom = 2.0
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)

            # Convert to numpy array for OpenCV processing
            img_data = pix.tobytes("png")
            img_pil = Image.open(BytesIO(img_data))
            img_array = np.array(img_pil)

            # Convert to grayscale
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            # Detect table using multiple techniques
            table_bbox = self._detect_table_from_lines(gray, zoom)

            pdf_doc.close()

            if table_bbox:
                logger.info(f"✅ Visual detection found table bbox: {table_bbox}")
                return table_bbox

            return None

        except Exception as e:
            logger.warning(f"⚠️ Visual table detection failed: {str(e)}")
            return None

    def _detect_table_from_lines(self, gray_image: np.ndarray, zoom_factor: float) -> Optional[Dict[str, float]]:
        """
        Detect table boundaries by finding horizontal and vertical lines
        """
        try:
            # Apply morphological operations to enhance lines
            kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))

            # Detect horizontal lines
            horizontal_lines = cv2.morphologyEx(gray_image, cv2.MORPH_OPEN, kernel_horizontal)
            horizontal_lines = cv2.dilate(horizontal_lines, kernel_horizontal, iterations=2)

            # Detect vertical lines
            vertical_lines = cv2.morphologyEx(gray_image, cv2.MORPH_OPEN, kernel_vertical)
            vertical_lines = cv2.dilate(vertical_lines, kernel_vertical, iterations=2)

            # Combine horizontal and vertical lines
            table_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)

            # Find contours
            contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None

            # Find the largest rectangular contour (likely the table)
            largest_area = 0
            best_bbox = None

            for contour in contours:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # Filter out small areas and non-rectangular shapes
                if area > 10000 and w > 100 and h > 50:  # Minimum size thresholds
                    if area > largest_area:
                        largest_area = area
                        # Convert back to PDF coordinates (account for zoom)
                        best_bbox = {
                            "x0": x / zoom_factor,
                            "y0": y / zoom_factor,
                            "x1": (x + w) / zoom_factor,
                            "y1": (y + h) / zoom_factor
                        }

            return best_bbox

        except Exception as e:
            logger.warning(f"⚠️ Line-based table detection failed: {str(e)}")
            return None

    async def _detect_table_bbox_from_content(self, pdf_path: str, page_number: int, table_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """
        Detect table bbox by analyzing text content and layout patterns
        """
        try:
            logger.info(f"📝 Attempting content-based table detection on page {page_number}")

            # Open PDF and extract text with positions
            pdf_doc = fitz.open(pdf_path)
            page_idx = max(0, page_number - 1)

            if page_idx >= len(pdf_doc):
                return None

            page = pdf_doc[page_idx]

            # Get text with bounding boxes
            text_dict = page.get_text("dict")

            # Analyze text blocks to find table-like patterns
            table_bbox = self._analyze_text_blocks_for_table(text_dict, table_data)

            pdf_doc.close()

            if table_bbox:
                logger.info(f"✅ Content-based detection found table bbox: {table_bbox}")
                return table_bbox

            return None

        except Exception as e:
            logger.warning(f"⚠️ Content-based table detection failed: {str(e)}")
            return None

    def _analyze_text_blocks_for_table(self, text_dict: dict, table_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """
        Analyze text blocks to identify table boundaries based on content patterns
        """
        try:
            if not text_dict.get("blocks"):
                return None

            # Extract text content from table data for matching
            table_text_content = set()

            # Get text from CSV content
            if "csv_content" in table_data:
                csv_lines = table_data["csv_content"].split('\n')
                for line in csv_lines:
                    words = line.replace(',', ' ').split()
                    table_text_content.update(word.strip().lower() for word in words if word.strip())

            # Get text from cells
            if "cells" in table_data:
                for cell in table_data["cells"]:
                    if hasattr(cell, 'text') and cell.text:
                        words = str(cell.text).split()
                        table_text_content.update(word.strip().lower() for word in words if word.strip())

            if not table_text_content:
                return None

            # Find text blocks that contain table content
            matching_blocks = []

            for block in text_dict["blocks"]:
                if block.get("type") == 0:  # Text block
                    block_text = ""
                    block_bbox = block.get("bbox", [0, 0, 0, 0])

                    # Extract text from lines
                    for line in block.get("lines", []):
                        for span in line.get("spans", []):
                            text = span.get("text", "")
                            block_text += text + " "

                    # Check if this block contains table content
                    block_words = set(word.strip().lower() for word in block_text.split() if word.strip())

                    # Calculate overlap with table content
                    overlap = len(table_text_content.intersection(block_words))
                    if overlap > 0:
                        matching_blocks.append({
                            "bbox": block_bbox,
                            "overlap": overlap,
                            "text": block_text[:100]  # First 100 chars for debugging
                        })

            if not matching_blocks:
                return None

            # Find the bounding box that encompasses all matching blocks
            min_x0 = min(block["bbox"][0] for block in matching_blocks)
            min_y0 = min(block["bbox"][1] for block in matching_blocks)
            max_x1 = max(block["bbox"][2] for block in matching_blocks)
            max_y1 = max(block["bbox"][3] for block in matching_blocks)

            # Add padding
            padding = 20
            content_bbox = {
                "x0": max(0, min_x0 - padding),
                "y0": max(0, min_y0 - padding),
                "x1": max_x1 + padding,
                "y1": max_y1 + padding
            }

            logger.info(f"📝 Content-based bbox from {len(matching_blocks)} matching blocks: {content_bbox}")
            return content_bbox

        except Exception as e:
            logger.warning(f"⚠️ Text block analysis failed: {str(e)}")
            return None

    async def _crop_table_image(self, pdf_path: str, bbox: Dict[str, float], page_number: int,
                               output_dir: Path, table_name: str) -> Optional[str]:
        """
        Crop table image from PDF using bbox coordinates

        Args:
            pdf_path: Path to PDF file
            bbox: Bounding box coordinates from Docling
            page_number: Page number (1-based)
            output_dir: Output directory
            table_name: Name for the table image file

        Returns:
            Path to cropped image file
        """
        try:
            logger.info(f"🖼️ Cropping table image: {table_name} from page {page_number}")

            # Open PDF with PyMuPDF
            pdf_doc = fitz.open(pdf_path)

            # Get the page (0-based indexing)
            page_idx = max(0, page_number - 1)
            if page_idx >= len(pdf_doc):
                logger.warning(f"⚠️ Page {page_number} not found in PDF")
                return None

            page = pdf_doc[page_idx]

            # Extract bbox coordinates
            # Docling bbox format may vary, handle different formats
            if isinstance(bbox, dict):
                # Common formats: {x0, y0, x1, y1} or {left, top, right, bottom}
                if 'x0' in bbox and 'y0' in bbox and 'x1' in bbox and 'y1' in bbox:
                    rect = fitz.Rect(bbox['x0'], bbox['y0'], bbox['x1'], bbox['y1'])
                elif 'left' in bbox and 'top' in bbox and 'right' in bbox and 'bottom' in bbox:
                    rect = fitz.Rect(bbox['left'], bbox['top'], bbox['right'], bbox['bottom'])
                else:
                    logger.warning(f"⚠️ Unknown bbox format: {bbox}")
                    return None
            elif isinstance(bbox, (list, tuple)) and len(bbox) == 4:
                # List format: [x0, y0, x1, y1]
                rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
            else:
                logger.warning(f"⚠️ Invalid bbox format: {bbox}")
                return None

            # Get page dimensions for validation
            page_rect = page.rect
            logger.info(f"📏 Page dimensions: {page_rect.width} x {page_rect.height}")
            logger.info(f"📦 Table bbox: {rect}")

            # Validate and adjust bbox if necessary
            original_rect = rect
            rect = rect & page_rect  # Intersect with page bounds

            if rect.is_empty:
                logger.warning(f"⚠️ Empty bbox after intersection with page bounds")
                logger.warning(f"📦 Original bbox: {original_rect}")
                logger.warning(f"📏 Page bounds: {page_rect}")

                # Try fallback methods if direct bbox fails
                logger.info(f"🔄 Attempting fallback detection methods...")

                # Fallback: Try visual detection
                fallback_bbox = await self._detect_table_bbox_visually(pdf_path, page_number, {"table_name": table_name})
                if fallback_bbox:
                    logger.info(f"✅ Fallback visual detection found bbox: {fallback_bbox}")
                    if 'x0' in fallback_bbox and 'y0' in fallback_bbox and 'x1' in fallback_bbox and 'y1' in fallback_bbox:
                        rect = fitz.Rect(fallback_bbox['x0'], fallback_bbox['y0'], fallback_bbox['x1'], fallback_bbox['y1'])
                        rect = rect & page_rect  # Ensure it's within page bounds
                        if not rect.is_empty:
                            logger.info(f"✅ Using fallback bbox for cropping")
                        else:
                            logger.warning(f"⚠️ Fallback bbox also outside page bounds")
                            return None
                    else:
                        logger.warning(f"⚠️ Invalid fallback bbox format")
                        return None
                else:
                    logger.warning(f"⚠️ All fallback methods failed")
                    return None

            # Render the cropped area as image
            # Use higher resolution for better quality
            zoom = 2.0  # 2x zoom for better quality
            mat = fitz.Matrix(zoom, zoom)

            # Get pixmap of the cropped area
            pix = page.get_pixmap(matrix=mat, clip=rect)

            # Convert to PIL Image
            img_data = pix.tobytes("png")
            img = Image.open(BytesIO(img_data))

            # Save image
            filename_base = Path(pdf_path).stem
            image_filename = f"{filename_base}_{table_name}_page_{page_number}.png"
            image_path = output_dir / image_filename

            img.save(image_path, "PNG", optimize=True)

            # Close PDF
            pdf_doc.close()

            logger.info(f"✅ Table image saved: {image_path}")
            return str(image_path)

        except Exception as e:
            logger.error(f"❌ Error cropping table image: {str(e)}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return None
