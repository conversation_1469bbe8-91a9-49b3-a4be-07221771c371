"""
Post Processing Pipeline
Handles data cleaning, format conversion, and export functionality
"""

import os
import json
import csv
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import pandas as pd
from pathlib import Path
import re

logger = logging.getLogger(__name__)

class PostProcessor:
    """Handles post-processing operations for RAG pipeline outputs"""
    
    def __init__(self, output_dir: str = "outputs"):
        """
        Initialize post processor
        
        Args:
            output_dir: Directory for output files
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        logger.info(f"PostProcessor initialized with output directory: {output_dir}")
    
    def json_to_csv(self, 
                   json_data: Union[Dict, List[Dict]], 
                   output_filename: str,
                   flatten_nested: bool = True) -> str:
        """
        Convert JSON data to CSV format
        
        Args:
            json_data: JSON data to convert
            output_filename: Name of output CSV file
            flatten_nested: Whether to flatten nested JSON objects
            
        Returns:
            Path to created CSV file
        """
        try:
            # Ensure json_data is a list
            if isinstance(json_data, dict):
                json_data = [json_data]
            
            if not json_data:
                raise ValueError("No data to convert")
            
            # Flatten nested objects if requested
            if flatten_nested:
                json_data = [self._flatten_dict(item) for item in json_data]
            
            # Create DataFrame
            df = pd.DataFrame(json_data)
            
            # Generate output path
            output_path = self.output_dir / f"{output_filename}.csv"
            
            # Save to CSV
            df.to_csv(output_path, index=False, encoding='utf-8')
            
            logger.info(f"Converted JSON to CSV: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error converting JSON to CSV: {str(e)}")
            raise
    
    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested dictionary"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert list to string representation
                items.append((new_key, str(v)))
            else:
                items.append((new_key, v))
        return dict(items)
    
    def clean_response_data(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and validate response data
        
        Args:
            response_data: Raw response data from LLM
            
        Returns:
            Cleaned response data
        """
        try:
            cleaned_data = response_data.copy()
            
            # Remove or fix common issues
            cleaned_data = self._remove_empty_fields(cleaned_data)
            cleaned_data = self._fix_encoding_issues(cleaned_data)
            cleaned_data = self._validate_json_structure(cleaned_data)
            cleaned_data = self._sanitize_text_fields(cleaned_data)
            
            # Add cleaning metadata
            cleaned_data['_metadata'] = {
                'cleaned_at': datetime.now().isoformat(),
                'cleaning_applied': True,
                'original_keys': list(response_data.keys())
            }
            
            logger.debug("Response data cleaned successfully")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"Error cleaning response data: {str(e)}")
            return response_data  # Return original if cleaning fails
    
    def _remove_empty_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove empty or null fields"""
        cleaned = {}
        for key, value in data.items():
            if value is not None and value != "" and value != []:
                if isinstance(value, dict):
                    cleaned_nested = self._remove_empty_fields(value)
                    if cleaned_nested:  # Only add if not empty after cleaning
                        cleaned[key] = cleaned_nested
                else:
                    cleaned[key] = value
        return cleaned
    
    def _fix_encoding_issues(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Fix common encoding issues in text fields"""
        def fix_text(text):
            if isinstance(text, str):
                # Fix common encoding issues
                text = text.replace('\u2019', "'")  # Right single quotation mark
                text = text.replace('\u2018', "'")  # Left single quotation mark
                text = text.replace('\u201c', '"')  # Left double quotation mark
                text = text.replace('\u201d', '"')  # Right double quotation mark
                text = text.replace('\u2013', '-')  # En dash
                text = text.replace('\u2014', '--') # Em dash
                text = text.replace('\xa0', ' ')    # Non-breaking space
                
                # Remove excessive whitespace
                text = re.sub(r'\s+', ' ', text).strip()
                
            return text
        
        def fix_recursive(obj):
            if isinstance(obj, dict):
                return {key: fix_recursive(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [fix_recursive(item) for item in obj]
            elif isinstance(obj, str):
                return fix_text(obj)
            else:
                return obj
        
        return fix_recursive(data)
    
    def _validate_json_structure(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and fix JSON structure issues"""
        try:
            # Test if data can be serialized to JSON
            json.dumps(data)
            return data
        except (TypeError, ValueError) as e:
            logger.warning(f"JSON structure issue detected: {str(e)}")
            
            # Try to fix common issues
            def make_serializable(obj):
                if isinstance(obj, dict):
                    return {str(key): make_serializable(value) for key, value in obj.items()}
                elif isinstance(obj, list):
                    return [make_serializable(item) for item in obj]
                elif isinstance(obj, (datetime,)):
                    return obj.isoformat()
                elif hasattr(obj, '__dict__'):
                    return str(obj)
                else:
                    try:
                        json.dumps(obj)
                        return obj
                    except:
                        return str(obj)
            
            return make_serializable(data)
    
    def _sanitize_text_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize text fields for safety and consistency"""
        def sanitize_text(text):
            if isinstance(text, str):
                # Remove potentially harmful content
                text = re.sub(r'<script.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
                text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)
                
                # Normalize line endings
                text = text.replace('\r\n', '\n').replace('\r', '\n')
                
                # Limit excessive length
                if len(text) > 10000:
                    text = text[:10000] + "... [truncated]"
                    
            return text
        
        def sanitize_recursive(obj):
            if isinstance(obj, dict):
                return {key: sanitize_recursive(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [sanitize_recursive(item) for item in obj]
            elif isinstance(obj, str):
                return sanitize_text(obj)
            else:
                return obj
        
        return sanitize_recursive(data)
    
    def export_search_results(self, 
                            search_results: List[Dict[str, Any]], 
                            query: str,
                            format: str = "csv") -> str:
        """
        Export search results to specified format
        
        Args:
            search_results: List of search result dictionaries
            query: Original search query
            format: Export format (csv, json, xlsx)
            
        Returns:
            Path to exported file
        """
        try:
            # Clean query for filename
            safe_query = re.sub(r'[^\w\s-]', '', query)[:50]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"search_results_{safe_query}_{timestamp}"
            
            if format.lower() == "csv":
                return self.json_to_csv(search_results, base_filename)
            
            elif format.lower() == "json":
                output_path = self.output_dir / f"{base_filename}.json"
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump({
                        "query": query,
                        "timestamp": datetime.now().isoformat(),
                        "results_count": len(search_results),
                        "results": search_results
                    }, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Exported search results to JSON: {output_path}")
                return str(output_path)
            
            elif format.lower() == "xlsx":
                df = pd.DataFrame(search_results)
                output_path = self.output_dir / f"{base_filename}.xlsx"
                
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='Search Results', index=False)
                    
                    # Add metadata sheet
                    metadata_df = pd.DataFrame([{
                        "Query": query,
                        "Timestamp": datetime.now().isoformat(),
                        "Results Count": len(search_results)
                    }])
                    metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
                
                logger.info(f"Exported search results to Excel: {output_path}")
                return str(output_path)
            
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting search results: {str(e)}")
            raise
    
    def generate_report(self, 
                       document_stats: Dict[str, Any],
                       search_history: List[Dict[str, Any]],
                       output_filename: str = None) -> str:
        """
        Generate a comprehensive report
        
        Args:
            document_stats: Statistics about processed documents
            search_history: History of search queries and results
            output_filename: Custom output filename
            
        Returns:
            Path to generated report
        """
        try:
            if not output_filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"rag_pipeline_report_{timestamp}"
            
            report_data = {
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "report_type": "RAG Pipeline Analysis",
                    "version": "1.0"
                },
                "document_statistics": document_stats,
                "search_analytics": self._analyze_search_history(search_history),
                "search_history": search_history
            }
            
            # Export as JSON
            json_path = self.output_dir / f"{output_filename}.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            # Also create a readable HTML report
            html_path = self._generate_html_report(report_data, output_filename)
            
            logger.info(f"Generated comprehensive report: {json_path}")
            return str(json_path)
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            raise
    
    def _analyze_search_history(self, search_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze search history for insights"""
        if not search_history:
            return {"total_searches": 0}
        
        total_searches = len(search_history)
        
        # Analyze query patterns
        query_lengths = [len(item.get("query", "")) for item in search_history]
        avg_query_length = sum(query_lengths) / len(query_lengths) if query_lengths else 0
        
        # Analyze response quality
        confidence_scores = [
            item.get("metadata", {}).get("confidence_score", 0) 
            for item in search_history
        ]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        # Most common sources
        all_sources = []
        for item in search_history:
            all_sources.extend(item.get("sources", []))
        
        source_counts = {}
        for source in all_sources:
            source_counts[source] = source_counts.get(source, 0) + 1
        
        top_sources = sorted(source_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_searches": total_searches,
            "average_query_length": round(avg_query_length, 1),
            "average_confidence_score": round(avg_confidence, 2),
            "top_sources": top_sources,
            "search_frequency": self._calculate_search_frequency(search_history)
        }
    
    def _calculate_search_frequency(self, search_history: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate search frequency by time period"""
        # This is a simplified implementation
        # In a real application, you'd parse timestamps and group by periods
        return {
            "today": len(search_history),  # Simplified
            "this_week": len(search_history),
            "this_month": len(search_history)
        }
    
    def _generate_html_report(self, report_data: Dict[str, Any], filename: str) -> str:
        """Generate HTML version of the report"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>RAG Pipeline Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .stats {{ display: flex; gap: 20px; }}
                .stat-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; flex: 1; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>RAG Pipeline Analysis Report</h1>
                <p>Generated: {report_data['report_metadata']['generated_at']}</p>
            </div>
            
            <div class="section">
                <h2>Document Statistics</h2>
                <div class="stats">
                    <div class="stat-box">
                        <h3>Total Documents</h3>
                        <p>{report_data.get('document_statistics', {}).get('total_documents', 'N/A')}</p>
                    </div>
                    <div class="stat-box">
                        <h3>Total Pages</h3>
                        <p>{report_data.get('document_statistics', {}).get('total_pages', 'N/A')}</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>Search Analytics</h2>
                <p>Total Searches: {report_data.get('search_analytics', {}).get('total_searches', 0)}</p>
                <p>Average Confidence: {report_data.get('search_analytics', {}).get('average_confidence_score', 0)}</p>
            </div>
        </body>
        </html>
        """
        
        html_path = self.output_dir / f"{filename}.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(html_path)
    
    def cleanup_old_files(self, days_old: int = 7) -> int:
        """
        Clean up old output files
        
        Args:
            days_old: Remove files older than this many days
            
        Returns:
            Number of files removed
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            
            removed_count = 0
            for file_path in self.output_dir.iterdir():
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    removed_count += 1
                    logger.debug(f"Removed old file: {file_path}")
            
            logger.info(f"Cleaned up {removed_count} old files")
            return removed_count
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
            return 0
