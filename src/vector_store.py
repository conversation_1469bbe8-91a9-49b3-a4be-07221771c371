"""
Vector Store Implementation using Supabase PostgreSQL with pgvector
Handles document storage, embedding generation, and similarity search
"""

import os
import logging
from typing import Dict, List, Optional, Any, Tuple
import asyncio
from datetime import datetime
import json

import numpy as np
from supabase import create_client, Client
import openai
from sentence_transformers import SentenceTransformer

logger = logging.getLogger(__name__)

class VectorStore:
    """Manages document storage and vector operations in Supabase"""
    
    def __init__(self, 
                 embedding_model: str = "text-embedding-3-small",
                 embedding_dimension: int = 1536):
        """
        Initialize vector store
        
        Args:
            embedding_model: OpenAI embedding model to use
            embedding_dimension: Dimension of embeddings
        """
        self.embedding_model = embedding_model
        self.embedding_dimension = embedding_dimension
        
        # Initialize Supabase client
        self.supabase = self._init_supabase()
        
        # Initialize OpenAI client
        openai.api_key = os.getenv("OPENAI_API_KEY")
        if not openai.api_key:
            logger.warning("OpenAI API key not found. Falling back to sentence-transformers.")
            self.use_openai = False
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            self.embedding_dimension = 384  # Dimension for all-MiniLM-L6-v2
        else:
            self.use_openai = True
        
        logger.info(f"VectorStore initialized with {embedding_model}")
    
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_KEY")
        
        if not url or not key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
        
        return create_client(url, key)
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text"""
        try:
            if self.use_openai:
                response = await openai.Embedding.acreate(
                    model=self.embedding_model,
                    input=text
                )
                return response['data'][0]['embedding']
            else:
                # Use sentence-transformers as fallback
                embedding = self.sentence_model.encode(text)
                return embedding.tolist()
                
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            # Return zero vector as fallback
            return [0.0] * self.embedding_dimension
    
    async def store_document(self, document_data: Dict[str, Any]) -> str:
        """
        Store document and its pages in the database
        
        Args:
            document_data: Processed document data from DocumentProcessor
            
        Returns:
            Document ID
        """
        try:
            # Insert document metadata
            doc_result = self.supabase.table("documents").insert({
                "filename": document_data["metadata"]["filename"],
                "markdown_data": document_data.get("markdown_data", ""),
                "title": document_data["metadata"].get("title", ""),
                "summary": document_data.get("summary", ""),
                "metadata": document_data["metadata"]
            }).execute()
            
            if not doc_result.data:
                raise Exception("Failed to insert document")
            
            document_id = doc_result.data[0]["id"]
            logger.info(f"Stored document: {document_id}")
            
            # Store pages with embeddings
            if "pages" in document_data:
                await self._store_pages(document_id, document_data["pages"])
            
            # Store chunks with embeddings
            if "chunks" in document_data:
                await self._store_chunks(document_id, document_data["chunks"])
            
            return document_id
            
        except Exception as e:
            logger.error(f"Error storing document: {str(e)}")
            raise
    
    async def _store_pages(self, document_id: str, pages_data: List[Dict[str, Any]]):
        """Store individual pages with embeddings"""
        for page_data in pages_data:
            try:
                # Generate embedding for page content
                page_text = page_data.get("markdown", "")
                embedding = await self.generate_embedding(page_text) if page_text else None
                
                # Prepare page data
                page_record = {
                    "file_ref_id": document_id,
                    "page_number": page_data["page_number"],
                    "markdown": page_text,
                    "summary": page_data.get("summary", ""),
                    "base_64_encoded_images": page_data.get("base_64_encoded_images", []),
                    "metadata": page_data.get("metadata", {}),
                    "embedding": embedding
                }
                
                # Insert page
                result = self.supabase.table("document_pages").insert(page_record).execute()
                
                if result.data:
                    logger.debug(f"Stored page {page_data['page_number']} for document {document_id}")
                
            except Exception as e:
                logger.error(f"Error storing page {page_data.get('page_number', 'unknown')}: {str(e)}")
    
    async def _store_chunks(self, document_id: str, chunks_data: List[Dict[str, Any]]):
        """Store text chunks as additional pages for better search granularity"""
        for i, chunk_data in enumerate(chunks_data):
            try:
                # Generate embedding for chunk
                chunk_text = chunk_data.get("text", "")
                embedding = await self.generate_embedding(chunk_text) if chunk_text else None
                
                # Store chunk as a special page (negative page numbers for chunks)
                chunk_record = {
                    "file_ref_id": document_id,
                    "page_number": -(i + 1),  # Negative page numbers for chunks
                    "markdown": chunk_text,
                    "summary": f"Text chunk {i+1}",
                    "base_64_encoded_images": [],
                    "metadata": {
                        **chunk_data,
                        "is_chunk": True,
                        "chunk_index": i
                    },
                    "embedding": embedding
                }
                
                # Insert chunk
                result = self.supabase.table("document_pages").insert(chunk_record).execute()
                
                if result.data:
                    logger.debug(f"Stored chunk {i+1} for document {document_id}")
                
            except Exception as e:
                logger.error(f"Error storing chunk {i}: {str(e)}")
    
    async def similarity_search(self, 
                              query: str, 
                              k: int = 5,
                              similarity_threshold: float = 0.7,
                              include_chunks: bool = True) -> List[Dict[str, Any]]:
        """
        Perform similarity search using vector embeddings
        
        Args:
            query: Search query
            k: Number of results to return
            similarity_threshold: Minimum similarity score
            include_chunks: Whether to include text chunks in search
            
        Returns:
            List of matching documents with similarity scores
        """
        try:
            # Generate embedding for query
            query_embedding = await self.generate_embedding(query)
            
            # Build search query
            search_conditions = []
            if not include_chunks:
                search_conditions.append("page_number > 0")
            
            # Perform vector similarity search
            query_builder = self.supabase.table("document_pages").select(
                """
                id,
                file_ref_id,
                page_number,
                markdown,
                summary,
                metadata,
                documents!inner(filename, title, summary),
                embedding <-> %s as similarity
                """ % query_embedding
            )
            
            if search_conditions:
                for condition in search_conditions:
                    query_builder = query_builder.filter(condition)
            
            result = query_builder.order("similarity").limit(k).execute()
            
            if not result.data:
                return []
            
            # Process and format results
            formatted_results = []
            for item in result.data:
                similarity_score = 1 - item.get("similarity", 1)  # Convert distance to similarity
                
                if similarity_score >= similarity_threshold:
                    formatted_results.append({
                        "id": item["id"],
                        "document_id": item["file_ref_id"],
                        "filename": item["documents"]["filename"],
                        "document_title": item["documents"]["title"],
                        "page_number": item["page_number"],
                        "content": item["markdown"],
                        "summary": item["summary"],
                        "similarity_score": similarity_score,
                        "metadata": item["metadata"],
                        "is_chunk": item.get("metadata", {}).get("is_chunk", False)
                    })
            
            logger.info(f"Found {len(formatted_results)} similar documents for query: {query[:50]}...")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error in similarity search: {str(e)}")
            return []
    
    async def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a document by ID"""
        try:
            result = self.supabase.table("documents").select("*").eq("id", document_id).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving document {document_id}: {str(e)}")
            return None
    
    async def get_document_pages(self, document_id: str) -> List[Dict[str, Any]]:
        """Retrieve all pages for a document"""
        try:
            result = self.supabase.table("document_pages").select("*").eq("file_ref_id", document_id).order("page_number").execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error retrieving pages for document {document_id}: {str(e)}")
            return []
    
    async def list_documents(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List all documents"""
        try:
            result = self.supabase.table("documents").select("*").order("created_at", desc=True).limit(limit).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error listing documents: {str(e)}")
            return []
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document and all its pages"""
        try:
            # Delete pages first (due to foreign key constraint)
            self.supabase.table("document_pages").delete().eq("file_ref_id", document_id).execute()
            
            # Delete document
            result = self.supabase.table("documents").delete().eq("id", document_id).execute()
            
            logger.info(f"Deleted document: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {str(e)}")
            return False
    
    async def hybrid_search(self, 
                          query: str, 
                          k: int = 5,
                          keyword_weight: float = 0.3,
                          vector_weight: float = 0.7) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining keyword and vector similarity
        
        Args:
            query: Search query
            k: Number of results to return
            keyword_weight: Weight for keyword search
            vector_weight: Weight for vector search
            
        Returns:
            List of matching documents with combined scores
        """
        try:
            # Get vector similarity results
            vector_results = await self.similarity_search(query, k=k*2)
            
            # Get keyword search results (simple text search)
            keyword_results = await self._keyword_search(query, k=k*2)
            
            # Combine and rank results
            combined_results = self._combine_search_results(
                vector_results, keyword_results, 
                vector_weight, keyword_weight
            )
            
            return combined_results[:k]
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {str(e)}")
            return []
    
    async def _keyword_search(self, query: str, k: int = 10) -> List[Dict[str, Any]]:
        """Perform keyword-based search"""
        try:
            # Use PostgreSQL full-text search
            result = self.supabase.table("document_pages").select(
                """
                id,
                file_ref_id,
                page_number,
                markdown,
                summary,
                metadata,
                documents!inner(filename, title, summary)
                """
            ).text_search("markdown", query).limit(k).execute()
            
            # Format results similar to vector search
            formatted_results = []
            for item in result.data or []:
                formatted_results.append({
                    "id": item["id"],
                    "document_id": item["file_ref_id"],
                    "filename": item["documents"]["filename"],
                    "document_title": item["documents"]["title"],
                    "page_number": item["page_number"],
                    "content": item["markdown"],
                    "summary": item["summary"],
                    "similarity_score": 0.8,  # Default score for keyword matches
                    "metadata": item["metadata"],
                    "is_chunk": item.get("metadata", {}).get("is_chunk", False)
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error in keyword search: {str(e)}")
            return []
    
    def _combine_search_results(self, 
                              vector_results: List[Dict[str, Any]], 
                              keyword_results: List[Dict[str, Any]],
                              vector_weight: float,
                              keyword_weight: float) -> List[Dict[str, Any]]:
        """Combine and rank vector and keyword search results"""
        combined = {}
        
        # Add vector results
        for result in vector_results:
            doc_id = result["id"]
            combined[doc_id] = result.copy()
            combined[doc_id]["combined_score"] = result["similarity_score"] * vector_weight
            combined[doc_id]["vector_score"] = result["similarity_score"]
            combined[doc_id]["keyword_score"] = 0.0
        
        # Add keyword results
        for result in keyword_results:
            doc_id = result["id"]
            if doc_id in combined:
                combined[doc_id]["combined_score"] += result["similarity_score"] * keyword_weight
                combined[doc_id]["keyword_score"] = result["similarity_score"]
            else:
                combined[doc_id] = result.copy()
                combined[doc_id]["combined_score"] = result["similarity_score"] * keyword_weight
                combined[doc_id]["vector_score"] = 0.0
                combined[doc_id]["keyword_score"] = result["similarity_score"]
        
        # Sort by combined score
        sorted_results = sorted(combined.values(), key=lambda x: x["combined_score"], reverse=True)
        
        return sorted_results
