"""
Document Processing Pipeline using Docling
Handles PDF parsing, markdown extraction, and content preparation
"""

import os
import base64
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Processes PDF documents using Docling for advanced parsing"""
    
    def __init__(self,
                 extract_images: bool = True,
                 use_page_chunks: bool = True):
        """
        Initialize document processor

        Args:
            extract_images: Whether to extract and encode images
            use_page_chunks: Whether to use page-based chunking (recommended)
        """
        self.extract_images = extract_images
        self.use_page_chunks = use_page_chunks
        
        # Configure Docling pipeline
        self.pipeline_options = PdfPipelineOptions()
        self.pipeline_options.do_ocr = True
        self.pipeline_options.do_table_structure = True
        self.pipeline_options.table_structure_options.do_cell_matching = True
        
        # Initialize document converter
        self.converter = DocumentConverter(
            format_options={
                InputFormat.PDF: self.pipeline_options,
            }
        )
        
        logger.info("DocumentProcessor initialized with Docling")
    
    async def process_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Process a PDF file and extract structured content
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Dictionary containing processed document data
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            logger.info(f"Processing PDF: {file_path.name}")
            
            # Convert document using Docling
            result = self.converter.convert(file_path)
            
            # Extract document metadata
            doc_metadata = {
                "filename": file_path.name,
                "file_size": file_path.stat().st_size,
                "num_pages": len(result.document.pages) if result.document.pages else 0,
                "title": self._extract_title(result),
                "creation_date": file_path.stat().st_ctime,
            }
            
            # Extract markdown content
            markdown_content = result.document.export_to_markdown()
            
            # Generate document summary
            summary = self._generate_summary(markdown_content)
            
            # Process pages individually
            pages_data = []
            if result.document.pages:
                for page_num, page in enumerate(result.document.pages, 1):
                    page_data = await self._process_page(page, page_num, result)
                    pages_data.append(page_data)
            
            # Create chunks for embedding (page-based or traditional)
            if self.use_page_chunks:
                chunks = self._create_page_chunks(pages_data, doc_metadata)
            else:
                chunks = self._create_word_chunks(markdown_content)

            return {
                "metadata": doc_metadata,
                "markdown_data": markdown_content,
                "summary": summary,
                "pages": pages_data,
                "chunks": chunks,
                "processing_status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error processing PDF {file_path}: {str(e)}")
            return {
                "metadata": {"filename": file_path.name if 'file_path' in locals() else "unknown"},
                "error": str(e),
                "processing_status": "failed"
            }
    
    async def _process_page(self, page: Any, page_num: int, result: Any) -> Dict[str, Any]:
        """Process individual page content with enhanced table detection"""
        try:
            # Extract page text/markdown
            page_markdown = ""
            if hasattr(page, 'export_to_markdown'):
                page_markdown = page.export_to_markdown()

            # Extract images if enabled
            images_b64 = []
            if self.extract_images and hasattr(result, 'document'):
                images_b64 = await self._extract_page_images(page, result)

            # Detect tables on this page
            tables_info = self._detect_page_tables(page, page_num)

            # Generate page summary
            page_summary = self._generate_page_summary(page_markdown)

            # Get first 100 characters for metadata
            first_100_chars = page_markdown[:100].strip() if page_markdown else ""

            return {
                "page_number": page_num,
                "markdown": page_markdown,
                "base_64_encoded_images": images_b64,
                "summary": page_summary,
                "tables_info": tables_info,
                "metadata": {
                    "word_count": len(page_markdown.split()) if page_markdown else 0,
                    "char_count": len(page_markdown) if page_markdown else 0,
                    "has_images": len(images_b64) > 0,
                    "image_count": len(images_b64),
                    "has_tables": len(tables_info) > 0,
                    "table_count": len(tables_info),
                    "first_100_chars": first_100_chars
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing page {page_num}: {str(e)}")
            return {
                "page_number": page_num,
                "markdown": "",
                "base_64_encoded_images": [],
                "summary": "",
                "metadata": {"error": str(e)},
                "processing_status": "failed"
            }
    
    async def _extract_page_images(self, page: Any, result: Any) -> List[str]:
        """Extract and encode images from a page"""
        images_b64 = []
        try:
            # This is a placeholder - actual implementation depends on Docling's API
            # for image extraction. You may need to adjust based on the specific
            # Docling version and available methods
            
            if hasattr(page, 'images') and page.images:
                for img in page.images:
                    if hasattr(img, 'pil_image'):
                        # Convert PIL image to base64
                        import io
                        buffer = io.BytesIO()
                        img.pil_image.save(buffer, format='PNG')
                        img_b64 = base64.b64encode(buffer.getvalue()).decode()
                        images_b64.append(img_b64)
            
        except Exception as e:
            logger.warning(f"Could not extract images from page: {str(e)}")
        
        return images_b64
    
    def _extract_title(self, result: Any) -> str:
        """Extract document title from conversion result"""
        try:
            # Try to get title from document metadata
            if hasattr(result.document, 'metadata') and result.document.metadata:
                if hasattr(result.document.metadata, 'title') and result.document.metadata.title:
                    return result.document.metadata.title
            
            # Fallback: extract from first heading in markdown
            markdown = result.document.export_to_markdown()
            lines = markdown.split('\n')
            for line in lines:
                if line.startswith('# '):
                    return line[2:].strip()
                elif line.startswith('## '):
                    return line[3:].strip()
            
            return "Untitled Document"
            
        except Exception as e:
            logger.warning(f"Could not extract title: {str(e)}")
            return "Untitled Document"
    
    def _generate_summary(self, content: str, max_length: int = 500) -> str:
        """Generate a summary of the document content"""
        if not content:
            return "No content available"
        
        # Simple extractive summary - take first few sentences
        sentences = content.replace('\n', ' ').split('. ')
        summary = ""
        for sentence in sentences:
            if len(summary + sentence) < max_length:
                summary += sentence + ". "
            else:
                break
        
        return summary.strip() or "Content available but summary could not be generated"
    
    def _generate_page_summary(self, content: str, max_length: int = 200) -> str:
        """Generate a summary for a single page"""
        if not content:
            return "No content on this page"
        
        # Take first paragraph or first few sentences
        paragraphs = content.split('\n\n')
        if paragraphs:
            first_para = paragraphs[0].strip()
            if len(first_para) <= max_length:
                return first_para
            else:
                # Truncate to max_length
                return first_para[:max_length-3] + "..."
        
        return "Page content available"
    
    def _create_page_chunks(self, pages_data: List[Dict[str, Any]], doc_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create page-based chunks for embedding with enhanced metadata"""
        if not pages_data:
            return []

        chunks = []
        for page_data in pages_data:
            page_content = page_data.get("markdown", "")
            if not page_content.strip():
                continue

            # Create enhanced content for tables
            enhanced_content = self._enhance_page_content_with_tables(page_data)

            chunk = {
                "text": enhanced_content,
                "page_number": page_data.get("page_number", 0),
                "chunk_type": "page",
                "word_count": len(enhanced_content.split()) if enhanced_content else 0,
                "char_count": len(enhanced_content) if enhanced_content else 0,
                "first_100_chars": enhanced_content[:100].strip() if enhanced_content else "",
                "has_tables": page_data.get("metadata", {}).get("has_tables", False),
                "table_count": page_data.get("metadata", {}).get("table_count", 0),
                "has_images": page_data.get("metadata", {}).get("has_images", False),
                "image_count": page_data.get("metadata", {}).get("image_count", 0),
                "document_title": doc_metadata.get("title", ""),
                "filename": doc_metadata.get("filename", "")
            }
            chunks.append(chunk)

        return chunks

    def _create_word_chunks(self, content: str) -> List[Dict[str, Any]]:
        """Create traditional overlapping word-based chunks for embedding (fallback)"""
        if not content:
            return []

        chunks = []
        words = content.split()
        chunk_size = 1000  # Default chunk size
        chunk_overlap = 200  # Default overlap

        for i in range(0, len(words), chunk_size - chunk_overlap):
            chunk_words = words[i:i + chunk_size]
            chunk_text = ' '.join(chunk_words)

            chunks.append({
                "text": chunk_text,
                "chunk_type": "word_based",
                "start_index": i,
                "end_index": min(i + chunk_size, len(words)),
                "word_count": len(chunk_words),
                "char_count": len(chunk_text),
                "first_100_chars": chunk_text[:100].strip() if chunk_text else ""
            })

        return chunks

    def _detect_page_tables(self, page: Any, page_num: int) -> List[Dict[str, Any]]:
        """Detect tables on a specific page"""
        tables_info = []
        try:
            # Check if page has tables
            if hasattr(page, 'tables') and page.tables:
                for i, table in enumerate(page.tables):
                    table_info = {
                        "table_index": i,
                        "page_number": page_num,
                        "has_structure": hasattr(table, 'data') or hasattr(table, 'cells'),
                        "estimated_rows": 0,
                        "estimated_cols": 0
                    }

                    # Try to get table dimensions
                    if hasattr(table, 'data') and table.data:
                        table_info["estimated_rows"] = len(table.data)
                        if table.data:
                            table_info["estimated_cols"] = len(table.data[0]) if isinstance(table.data[0], list) else 1
                    elif hasattr(table, 'cells') and table.cells:
                        # Estimate from cells
                        max_row = max(cell.get('row', 0) for cell in table.cells if isinstance(cell, dict)) + 1
                        max_col = max(cell.get('col', 0) for cell in table.cells if isinstance(cell, dict)) + 1
                        table_info["estimated_rows"] = max_row
                        table_info["estimated_cols"] = max_col

                    tables_info.append(table_info)

        except Exception as e:
            logger.warning(f"Error detecting tables on page {page_num}: {str(e)}")

        return tables_info

    def _enhance_page_content_with_tables(self, page_data: Dict[str, Any]) -> str:
        """Enhance page content by adding table context for better searchability"""
        base_content = page_data.get("markdown", "")
        tables_info = page_data.get("tables_info", [])

        if not tables_info:
            return base_content

        # Add table context information
        table_context = []
        for table_info in tables_info:
            context = f"\n[TABLE {table_info['table_index'] + 1} on page {table_info['page_number']}]"
            if table_info.get("estimated_rows", 0) > 0:
                context += f" - {table_info['estimated_rows']} rows x {table_info['estimated_cols']} columns"
            table_context.append(context)

        # Combine base content with table context
        enhanced_content = base_content
        if table_context:
            enhanced_content += "\n\nTables on this page:" + "".join(table_context)
            enhanced_content += "\n[End of table information]"

        return enhanced_content
    
    async def process_multiple_pdfs(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        """Process multiple PDF files concurrently"""
        tasks = [self.process_pdf(file_path) for file_path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "metadata": {"filename": Path(file_paths[i]).name},
                    "error": str(result),
                    "processing_status": "failed"
                })
            else:
                processed_results.append(result)
        
        return processed_results

# Utility functions
def validate_pdf_file(file_path: str) -> bool:
    """Validate if file is a valid PDF"""
    try:
        path = Path(file_path)
        return path.exists() and path.suffix.lower() == '.pdf' and path.stat().st_size > 0
    except Exception:
        return False

def get_file_info(file_path: str) -> Dict[str, Any]:
    """Get basic file information"""
    try:
        path = Path(file_path)
        stat = path.stat()
        return {
            "filename": path.name,
            "size_bytes": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "created": stat.st_ctime,
            "modified": stat.st_mtime,
            "extension": path.suffix.lower()
        }
    except Exception as e:
        return {"error": str(e)}
