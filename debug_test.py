"""
Debug test script to verify RAG system functionality
"""

import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from src.langchain_processor import AdvancedRAGProcessor

async def test_rag_system():
    """Test the RAG system with detailed debugging"""
    
    logger.info("🚀 Starting RAG system test...")
    
    # Initialize processor
    processor = AdvancedRAGProcessor()
    logger.info("✅ Processor initialized")
    
    # Test with sample PDFs (if they exist)
    sample_pdfs = [
        "M-500 MAIN AIR COMPRESSOR.pdf",
        "TANK CLEANING MACHINE.pdf"
    ]
    
    existing_pdfs = [pdf for pdf in sample_pdfs if Path(pdf).exists()]
    
    if not existing_pdfs:
        logger.warning("⚠️ No sample PDFs found. Please upload PDFs through the Gradio interface.")
        return
    
    logger.info(f"📄 Found {len(existing_pdfs)} PDFs: {existing_pdfs}")
    
    try:
        # Process PDFs
        logger.info("🔄 Processing PDFs...")
        results = await processor.process_multiple_pdfs(existing_pdfs)
        
        logger.info(f"📊 Processing results:")
        logger.info(f"  - Processed: {len(results['processed'])}")
        logger.info(f"  - Failed: {len(results['failed'])}")
        logger.info(f"  - Total documents: {results['total_documents']}")
        
        if results['total_documents'] == 0:
            logger.error("❌ No documents were processed successfully")
            return
        
        # Setup chains
        logger.info("🔗 Setting up chains...")
        processor.setup_chains("openai")
        logger.info("✅ Chains setup complete")
        
        # Test queries
        test_queries = [
            "What are the main components?",
            "List all spare parts",
            "What is the operating pressure?"
        ]
        
        for query in test_queries:
            logger.info(f"\n🔍 Testing query: '{query}'")
            
            # Test LangChain
            logger.info("📝 Testing LangChain...")
            lc_result = await processor.query_documents(query)
            logger.info(f"  Answer length: {len(lc_result.get('answer', ''))}")
            logger.info(f"  Sources: {len(lc_result.get('source_documents', []))}")
            logger.info(f"  Has error: {lc_result.get('error', False)}")
            
            # Test LlamaIndex
            logger.info("🦙 Testing LlamaIndex...")
            li_result = await processor.query_with_llama_index(query)
            logger.info(f"  Answer length: {len(li_result.get('answer', ''))}")
            logger.info(f"  Sources: {len(li_result.get('source_nodes', []))}")
            logger.info(f"  Has error: {li_result.get('error', False)}")
            
            # Show first 100 chars of answers
            if not lc_result.get('error'):
                logger.info(f"  LangChain answer preview: {lc_result.get('answer', '')[:100]}...")
            if not li_result.get('error'):
                logger.info(f"  LlamaIndex answer preview: {li_result.get('answer', '')[:100]}...")
        
        logger.info("🎉 Test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")

def main():
    """Main function"""
    asyncio.run(test_rag_system())

if __name__ == "__main__":
    main()
