#!/usr/bin/env python3
"""
Database setup script for Gradio Docling RAG Pipeline
Creates necessary tables and enables pgvector extension in Supabase
"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

def get_supabase_client() -> Client:
    """Initialize Supabase client"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")  # Use service key for admin operations
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file")
    
    return create_client(url, key)

def setup_database():
    """Set up database tables and extensions"""
    supabase = get_supabase_client()
    
    print("🚀 Setting up database...")
    
    # Enable pgvector extension
    print("📦 Enabling pgvector extension...")
    try:
        supabase.rpc("enable_pgvector").execute()
        print("✅ pgvector extension enabled")
    except Exception as e:
        print(f"⚠️  pgvector extension may already be enabled: {e}")
    
    # Create documents table
    print("📄 Creating documents table...")
    documents_sql = """
    CREATE TABLE IF NOT EXISTS documents (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        filename TEXT NOT NULL,
        markdown_data TEXT,
        title TEXT,
        summary TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    try:
        supabase.rpc("exec_sql", {"sql": documents_sql}).execute()
        print("✅ Documents table created")
    except Exception as e:
        print(f"❌ Error creating documents table: {e}")
    
    # Create document_pages table
    print("📑 Creating document_pages table...")
    pages_sql = """
    CREATE TABLE IF NOT EXISTS document_pages (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        file_ref_id UUID REFERENCES documents(id) ON DELETE CASCADE,
        page_number INTEGER NOT NULL,
        base_64_encoded_data TEXT,
        base_64_encoded_images TEXT[],
        markdown TEXT,
        embedding VECTOR(1536),
        summary TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    try:
        supabase.rpc("exec_sql", {"sql": pages_sql}).execute()
        print("✅ Document pages table created")
    except Exception as e:
        print(f"❌ Error creating document_pages table: {e}")
    
    # Create indexes for better performance
    print("🔍 Creating indexes...")
    indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename);",
        "CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_pages_file_ref_id ON document_pages(file_ref_id);",
        "CREATE INDEX IF NOT EXISTS idx_pages_page_number ON document_pages(page_number);",
        "CREATE INDEX IF NOT EXISTS idx_pages_embedding ON document_pages USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);"
    ]
    
    for idx_sql in indexes_sql:
        try:
            supabase.rpc("exec_sql", {"sql": idx_sql}).execute()
            print(f"✅ Index created: {idx_sql.split('idx_')[1].split(' ')[0]}")
        except Exception as e:
            print(f"⚠️  Index may already exist: {e}")
    
    # Create RLS policies (Row Level Security)
    print("🔒 Setting up Row Level Security...")
    rls_sql = [
        "ALTER TABLE documents ENABLE ROW LEVEL SECURITY;",
        "ALTER TABLE document_pages ENABLE ROW LEVEL SECURITY;",
        """
        CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON documents
        FOR SELECT USING (true);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Enable insert access for all users" ON documents
        FOR INSERT WITH CHECK (true);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON document_pages
        FOR SELECT USING (true);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Enable insert access for all users" ON document_pages
        FOR INSERT WITH CHECK (true);
        """
    ]
    
    for rls in rls_sql:
        try:
            supabase.rpc("exec_sql", {"sql": rls}).execute()
            print("✅ RLS policy applied")
        except Exception as e:
            print(f"⚠️  RLS policy may already exist: {e}")
    
    print("🎉 Database setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Copy .env.example to .env and fill in your API keys")
    print("2. Run: pip install -r requirements.txt")
    print("3. Run: python app.py")

def verify_setup():
    """Verify database setup"""
    supabase = get_supabase_client()
    
    print("🔍 Verifying database setup...")
    
    try:
        # Check if tables exist
        documents = supabase.table("documents").select("*").limit(1).execute()
        pages = supabase.table("document_pages").select("*").limit(1).execute()
        
        print("✅ Database tables are accessible")
        print(f"📊 Documents table: {len(documents.data)} records")
        print(f"📊 Document pages table: {len(pages.data)} records")
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        setup_database()
        if verify_setup():
            print("\n🎯 Database is ready for use!")
        else:
            print("\n⚠️  Database setup may have issues. Please check the logs above.")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        print("\n💡 Make sure you have:")
        print("1. Created a Supabase project")
        print("2. Enabled the pgvector extension in your Supabase project")
        print("3. Set SUPABASE_URL and SUPABASE_SERVICE_KEY in your .env file")
        sys.exit(1)
