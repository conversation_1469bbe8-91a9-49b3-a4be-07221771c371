#!/usr/bin/env python3
"""
Gradio Docling RAG Pipeline - Main Application
Interactive web interface for document processing and querying
"""

import os
import sys
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
import tempfile

import gradio as gr
from dotenv import load_dotenv

# Import our modules
from src.document_processor import DocumentProcessor, validate_pdf_file
from src.vector_store import VectorStore
from src.llm_handler import LLMHandler, LLMProvider
from src.post_processor import PostProcessor

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RAGPipeline:
    """Main RAG Pipeline orchestrator"""
    
    def __init__(self):
        """Initialize the RAG pipeline components"""
        try:
            self.document_processor = DocumentProcessor()
            self.vector_store = VectorStore()
            self.llm_handler = LLMHandler()
            self.post_processor = PostProcessor()
            
            # State management
            self.processing_status = {}
            self.search_history = []
            
            logger.info("RAG Pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG Pipeline: {str(e)}")
            raise
    
    async def process_documents(self, files: List[str], progress_callback=None) -> Dict[str, Any]:
        """Process uploaded documents"""
        results = {
            "processed": [],
            "failed": [],
            "total_files": len(files),
            "total_pages": 0
        }
        
        for i, file_path in enumerate(files):
            try:
                if progress_callback:
                    progress_callback(f"Processing {Path(file_path).name}...")
                
                # Validate file
                if not validate_pdf_file(file_path):
                    results["failed"].append({
                        "filename": Path(file_path).name,
                        "error": "Invalid PDF file"
                    })
                    continue
                
                # Process document
                doc_result = await self.document_processor.process_pdf(file_path)
                
                if doc_result.get("processing_status") == "success":
                    # Store in vector database
                    doc_id = await self.vector_store.store_document(doc_result)
                    
                    results["processed"].append({
                        "filename": doc_result["metadata"]["filename"],
                        "document_id": doc_id,
                        "pages": doc_result["metadata"].get("num_pages", 0),
                        "title": doc_result["metadata"].get("title", ""),
                        "summary": doc_result.get("summary", "")[:200] + "..."
                    })
                    
                    results["total_pages"] += doc_result["metadata"].get("num_pages", 0)
                    
                else:
                    results["failed"].append({
                        "filename": doc_result["metadata"]["filename"],
                        "error": doc_result.get("error", "Processing failed")
                    })
                
                if progress_callback:
                    progress = (i + 1) / len(files) * 100
                    progress_callback(f"Processed {i + 1}/{len(files)} files ({progress:.1f}%)")
                    
            except Exception as e:
                logger.error(f"Error processing {file_path}: {str(e)}")
                results["failed"].append({
                    "filename": Path(file_path).name,
                    "error": str(e)
                })
        
        return results
    
    async def search_documents(self, 
                             query: str, 
                             search_type: str = "hybrid",
                             max_results: int = 5) -> Dict[str, Any]:
        """Search documents using the specified method"""
        try:
            if search_type == "vector":
                results = await self.vector_store.similarity_search(query, k=max_results)
            elif search_type == "hybrid":
                results = await self.vector_store.hybrid_search(query, k=max_results)
            else:
                results = await self.vector_store.similarity_search(query, k=max_results, include_chunks=False)
            
            return {
                "results": results,
                "query": query,
                "search_type": search_type,
                "total_results": len(results)
            }
            
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return {
                "results": [],
                "query": query,
                "error": str(e)
            }
    
    async def generate_answer(self, 
                            query: str, 
                            context_results: List[Dict[str, Any]],
                            response_format: str = "json",
                            llm_provider: str = "openai") -> Dict[str, Any]:
        """Generate answer using LLM with context"""
        try:
            provider = LLMProvider(llm_provider.lower())
            
            response = await self.llm_handler.generate_response(
                query=query,
                context_chunks=context_results,
                response_format=response_format,
                provider=provider
            )
            
            # Clean the response
            cleaned_response = self.post_processor.clean_response_data(response)
            
            # Add to search history
            self.search_history.append(cleaned_response)
            
            return cleaned_response
            
        except Exception as e:
            logger.error(f"Error generating answer: {str(e)}")
            return {
                "response": {"error": str(e)},
                "query": query,
                "error": True
            }

# Initialize the pipeline
try:
    rag_pipeline = RAGPipeline()
    logger.info("RAG Pipeline ready")
except Exception as e:
    logger.error(f"Failed to initialize pipeline: {str(e)}")
    sys.exit(1)

# Gradio Interface Functions
def upload_and_process_files(files):
    """Handle file upload and processing"""
    if not files:
        return "No files uploaded.", ""
    
    try:
        # Create temporary copies of uploaded files
        temp_files = []
        for file in files:
            temp_files.append(file.name)
        
        # Process files
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        results = loop.run_until_complete(
            rag_pipeline.process_documents(temp_files)
        )
        
        # Format results
        status_msg = f"Processing completed!\n"
        status_msg += f"✅ Successfully processed: {len(results['processed'])} files\n"
        status_msg += f"❌ Failed: {len(results['failed'])} files\n"
        status_msg += f"📄 Total pages: {results['total_pages']}\n"
        
        if results['failed']:
            status_msg += "\nFailed files:\n"
            for failed in results['failed']:
                status_msg += f"- {failed['filename']}: {failed['error']}\n"
        
        # Format processed files info
        processed_info = ""
        if results['processed']:
            processed_info = "Successfully processed documents:\n\n"
            for doc in results['processed']:
                processed_info += f"📄 **{doc['filename']}**\n"
                processed_info += f"   - Title: {doc['title']}\n"
                processed_info += f"   - Pages: {doc['pages']}\n"
                processed_info += f"   - Summary: {doc['summary']}\n\n"
        
        return status_msg, processed_info
        
    except Exception as e:
        error_msg = f"Error processing files: {str(e)}"
        logger.error(error_msg)
        return error_msg, ""

def search_and_answer(query, search_type, response_format, llm_provider, max_results):
    """Handle search and answer generation"""
    if not query.strip():
        return "Please enter a search query.", "", ""
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Search documents
        search_results = loop.run_until_complete(
            rag_pipeline.search_documents(query, search_type, max_results)
        )
        
        if not search_results.get("results"):
            return "No relevant documents found.", "", ""
        
        # Generate answer
        answer_result = loop.run_until_complete(
            rag_pipeline.generate_answer(
                query, 
                search_results["results"], 
                response_format, 
                llm_provider
            )
        )
        
        # Format response
        if answer_result.get("error"):
            return f"Error generating answer: {answer_result.get('response', {}).get('error', 'Unknown error')}", "", ""
        
        # Format answer
        response_text = ""
        if response_format == "json":
            response_data = answer_result.get("response", {})
            if isinstance(response_data, dict):
                response_text = f"**Answer:** {response_data.get('answer', 'No answer provided')}\n\n"
                
                if response_data.get('key_points'):
                    response_text += "**Key Points:**\n"
                    for point in response_data['key_points']:
                        response_text += f"• {point}\n"
                    response_text += "\n"
                
                if response_data.get('confidence'):
                    response_text += f"**Confidence:** {response_data['confidence']}\n\n"
            else:
                response_text = str(response_data)
        else:
            response_text = str(answer_result.get("response", ""))
        
        # Format sources
        sources_text = "**Sources Used:**\n"
        for i, result in enumerate(search_results["results"], 1):
            sources_text += f"{i}. **{result['filename']}** (Page {result['page_number']}) - Similarity: {result['similarity_score']:.2f}\n"
            sources_text += f"   {result['content'][:200]}...\n\n"
        
        # Format metadata
        metadata_text = f"**Search Metadata:**\n"
        metadata_text += f"- Search Type: {search_type}\n"
        metadata_text += f"- Results Found: {len(search_results['results'])}\n"
        metadata_text += f"- LLM Provider: {llm_provider}\n"
        metadata_text += f"- Response Format: {response_format}\n"
        metadata_text += f"- Confidence Score: {answer_result.get('metadata', {}).get('confidence_score', 'N/A')}\n"
        
        return response_text, sources_text, metadata_text
        
    except Exception as e:
        error_msg = f"Error processing query: {str(e)}"
        logger.error(error_msg)
        return error_msg, "", ""

def list_documents():
    """List all processed documents"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        documents = loop.run_until_complete(rag_pipeline.vector_store.list_documents())
        
        if not documents:
            return "No documents found in the database."
        
        doc_list = "**Processed Documents:**\n\n"
        for doc in documents:
            doc_list += f"📄 **{doc['filename']}**\n"
            doc_list += f"   - Title: {doc.get('title', 'N/A')}\n"
            doc_list += f"   - Created: {doc.get('created_at', 'N/A')}\n"
            doc_list += f"   - Summary: {doc.get('summary', 'N/A')[:100]}...\n\n"
        
        return doc_list
        
    except Exception as e:
        return f"Error listing documents: {str(e)}"

def export_results(query, format_type):
    """Export search results"""
    if not rag_pipeline.search_history:
        return "No search history to export."
    
    try:
        # Get the latest search results
        latest_search = rag_pipeline.search_history[-1]
        
        # Export using post processor
        output_path = rag_pipeline.post_processor.export_search_results(
            [latest_search], 
            query or "latest_search", 
            format_type
        )
        
        return f"Results exported to: {output_path}"
        
    except Exception as e:
        return f"Error exporting results: {str(e)}"

# Create Gradio Interface
def create_interface():
    """Create the Gradio interface"""
    
    with gr.Blocks(title="Gradio Docling RAG Pipeline", theme=gr.themes.Soft()) as interface:
        gr.Markdown("""
        # 📄 Gradio Docling RAG Pipeline
        
        Upload PDF documents, process them with advanced AI, and ask questions to get intelligent answers.
        
        ## 🚀 Features
        - **Advanced PDF Processing** with Docling
        - **Vector Search** with Supabase PostgreSQL
        - **Multiple LLM Providers** (OpenAI, Anthropic, Mistral)
        - **Hybrid Search** combining keyword and semantic search
        - **Export Results** to CSV, JSON, or Excel
        """)
        
        with gr.Tab("📤 Upload & Process Documents"):
            with gr.Row():
                file_upload = gr.File(
                    label="Upload PDF Documents",
                    file_count="multiple",
                    file_types=[".pdf"]
                )
            
            process_btn = gr.Button("🔄 Process Documents", variant="primary")
            
            with gr.Row():
                process_status = gr.Textbox(
                    label="Processing Status",
                    lines=5,
                    interactive=False
                )
                processed_docs = gr.Markdown(
                    label="Processed Documents",
                    value="No documents processed yet."
                )
        
        with gr.Tab("🔍 Search & Query"):
            with gr.Row():
                with gr.Column(scale=3):
                    query_input = gr.Textbox(
                        label="Enter your question",
                        placeholder="e.g., What are the safety procedures for the air compressor?",
                        lines=2
                    )
                
                with gr.Column(scale=1):
                    search_type = gr.Dropdown(
                        choices=["hybrid", "vector", "pages_only"],
                        value="hybrid",
                        label="Search Type"
                    )
                    
                    response_format = gr.Dropdown(
                        choices=["json", "text", "markdown"],
                        value="json",
                        label="Response Format"
                    )
                    
                    llm_provider = gr.Dropdown(
                        choices=["openai", "anthropic", "mistral"],
                        value="openai",
                        label="LLM Provider"
                    )
                    
                    max_results = gr.Slider(
                        minimum=1,
                        maximum=10,
                        value=5,
                        step=1,
                        label="Max Results"
                    )
            
            search_btn = gr.Button("🔍 Search & Answer", variant="primary")
            
            with gr.Row():
                with gr.Column():
                    answer_output = gr.Markdown(label="Answer")
                
                with gr.Column():
                    sources_output = gr.Markdown(label="Sources")
            
            metadata_output = gr.Markdown(label="Search Metadata")
        
        with gr.Tab("📋 Document Management"):
            list_btn = gr.Button("📄 List All Documents")
            documents_list = gr.Markdown(value="Click 'List All Documents' to see processed documents.")
            
            with gr.Row():
                export_format = gr.Dropdown(
                    choices=["csv", "json", "xlsx"],
                    value="csv",
                    label="Export Format"
                )
                export_btn = gr.Button("💾 Export Latest Results")
            
            export_status = gr.Textbox(label="Export Status", interactive=False)
        
        # Event handlers
        process_btn.click(
            fn=upload_and_process_files,
            inputs=[file_upload],
            outputs=[process_status, processed_docs]
        )
        
        search_btn.click(
            fn=search_and_answer,
            inputs=[query_input, search_type, response_format, llm_provider, max_results],
            outputs=[answer_output, sources_output, metadata_output]
        )
        
        list_btn.click(
            fn=list_documents,
            outputs=[documents_list]
        )
        
        export_btn.click(
            fn=export_results,
            inputs=[query_input, export_format],
            outputs=[export_status]
        )
    
    return interface

if __name__ == "__main__":
    # Create and launch the interface
    interface = create_interface()
    
    # Launch with configuration
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,  # Set to True to create a public link
        debug=True
    )
