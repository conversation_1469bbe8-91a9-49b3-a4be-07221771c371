"""
Automated test for table extraction with image cropping
Tests with sample PDF files and saves results to output directory
"""

import asyncio
import logging
from pathlib import Path
import json
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our table extractor
from src.table_extractor import AdvancedTableExtractor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_table_extraction():
    """Test table extraction with sample files"""
    
    logger.info("🚀 Starting automated table extraction test...")
    
    # Initialize extractor
    extractor = AdvancedTableExtractor()
    
    # Define sample files
    sample_dir = Path("sample")
    output_dir = Path("output")
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Find PDF files in sample directory
    pdf_files = list(sample_dir.glob("*.pdf"))
    
    if not pdf_files:
        logger.error("❌ No PDF files found in sample directory")
        return
    
    logger.info(f"📄 Found {len(pdf_files)} PDF files: {[f.name for f in pdf_files]}")
    
    # Process each PDF
    all_results = []
    
    for pdf_file in pdf_files:
        logger.info(f"\n🔄 Processing: {pdf_file.name}")
        logger.info("=" * 50)
        
        try:
            # Extract tables with image cropping
            result = await extractor.extract_tables_from_pdf(str(pdf_file), str(output_dir))
            
            # Log results
            logger.info(f"📊 Results for {pdf_file.name}:")
            logger.info(f"  - Status: {result['processing_status']}")
            logger.info(f"  - Tables found: {result['total_tables']}")
            logger.info(f"  - Spare parts: {len(result.get('spare_parts', []))}")
            
            if result.get('error'):
                logger.error(f"  - Error: {result['error']}")
            
            # Log table details
            for table in result.get('tables', []):
                logger.info(f"  📋 Table {table['table_id']}:")
                logger.info(f"    - Page: {table['page_number']}")
                logger.info(f"    - CSV length: {len(table['csv_content'])} chars")
                logger.info(f"    - Image: {table.get('image_path', 'No image')}")
                logger.info(f"    - Bbox: {table.get('bbox', 'No bbox')}")
            
            # Save detailed results to JSON
            result_file = output_dir / f"{pdf_file.stem}_extraction_results.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                # Make a copy without potentially large CSV content for JSON
                json_result = result.copy()
                for table in json_result.get('tables', []):
                    if len(table.get('csv_content', '')) > 1000:
                        table['csv_content'] = table['csv_content'][:1000] + "... (truncated)"
                
                json.dump(json_result, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"💾 Detailed results saved to: {result_file}")
            
            # Save individual CSV files
            saved_files = extractor.save_tables_to_files(result, str(output_dir))
            logger.info(f"📁 Saved {len(saved_files)} files:")
            for file_path in saved_files:
                logger.info(f"  - {file_path}")
            
            all_results.append(result)
            
        except Exception as e:
            logger.error(f"❌ Error processing {pdf_file.name}: {str(e)}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
    
    # Create summary report
    logger.info("\n" + "=" * 60)
    logger.info("📊 EXTRACTION SUMMARY REPORT")
    logger.info("=" * 60)
    
    total_files = len(all_results)
    total_tables = sum(r['total_tables'] for r in all_results)
    total_spare_parts = sum(len(r.get('spare_parts', [])) for r in all_results)
    successful_files = len([r for r in all_results if r['processing_status'] == 'success'])
    
    logger.info(f"📄 Files processed: {total_files}")
    logger.info(f"✅ Successful: {successful_files}")
    logger.info(f"❌ Failed: {total_files - successful_files}")
    logger.info(f"📊 Total tables extracted: {total_tables}")
    logger.info(f"🔧 Total spare parts found: {total_spare_parts}")
    
    # Create combined summary CSV
    if all_results:
        summary_data = []
        for result in all_results:
            for table in result.get('tables', []):
                summary_data.append({
                    'filename': result['filename'],
                    'table_id': table['table_id'],
                    'page_number': table['page_number'],
                    'csv_length': len(table['csv_content']),
                    'has_image': bool(table.get('image_path')),
                    'image_path': table.get('image_path', ''),
                    'has_bbox': bool(table.get('bbox')),
                    'bbox': str(table.get('bbox', ''))
                })
        
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_file = output_dir / "extraction_summary.csv"
            summary_df.to_csv(summary_file, index=False)
            logger.info(f"📋 Summary report saved to: {summary_file}")
    
    # List all output files
    output_files = list(output_dir.glob("*"))
    logger.info(f"\n📁 Output directory contains {len(output_files)} files:")
    for file_path in sorted(output_files):
        file_size = file_path.stat().st_size if file_path.is_file() else 0
        file_type = "📄" if file_path.suffix == ".csv" else "🖼️" if file_path.suffix == ".png" else "📋" if file_path.suffix == ".json" else "📁"
        logger.info(f"  {file_type} {file_path.name} ({file_size:,} bytes)")
    
    logger.info("\n🎉 Table extraction test completed!")
    logger.info(f"📂 Check the '{output_dir}' directory for all extracted files")

def main():
    """Main function"""
    try:
        asyncio.run(test_table_extraction())
    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
