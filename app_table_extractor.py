"""
Specialized Table Extraction Application
Focus on extracting complete tables as CSV data from PDFs
"""

import os
import asyncio
import logging
from typing import List, Dict, Any, Optional, <PERSON>ple
import gradio as gr
from pathlib import Path
import json
import pandas as pd
import zipfile
from io import BytesIO

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import our table extractor
from src.table_extractor import AdvancedTableExtractor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TableExtractionApp:
    """Specialized application for table extraction from PDFs"""
    
    def __init__(self):
        """Initialize the application"""
        self.extractor = AdvancedTableExtractor()
        self.extraction_results = {}
        self.processing_status = "Ready"
        
        logger.info("Table Extraction App initialized")
    
    def extract_tables_sync(self, files: List[str]) -> Tuple[str, str, str]:
        """Extract tables from uploaded files synchronously"""
        try:
            if not files:
                return "❌ No files provided", "", ""
            
            # Filter PDF files
            pdf_files = [f for f in files if f.lower().endswith('.pdf')]
            
            if not pdf_files:
                return "❌ No PDF files found", "", ""
            
            self.processing_status = f"Processing {len(pdf_files)} PDF files..."
            logger.info(f"Processing {len(pdf_files)} PDF files for table extraction")
            
            all_results = []
            summary_info = []
            
            # Process each PDF
            for pdf_file in pdf_files:
                logger.info(f"🔄 Processing: {Path(pdf_file).name}")
                
                # Extract tables using asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(self.extractor.extract_tables_from_pdf(pdf_file))
                finally:
                    loop.close()
                
                all_results.append(result)
                
                # Create summary
                summary_info.append(f"""
**📄 {result['filename']}**
- Tables found: {result['total_tables']}
- Spare parts extracted: {len(result.get('spare_parts', []))}
- Status: {result['processing_status']}
{f"- Error: {result.get('error', '')}" if result.get('error') else ""}
""")
            
            # Store results
            self.extraction_results = {
                "files": all_results,
                "total_files": len(all_results),
                "total_tables": sum(r['total_tables'] for r in all_results),
                "total_spare_parts": sum(len(r.get('spare_parts', [])) for r in all_results)
            }
            
            # Create status message
            status_msg = f"""
✅ **Table Extraction Complete!**

📊 **Summary:**
- Files processed: {len(all_results)}
- Total tables found: {self.extraction_results['total_tables']}
- Total spare parts: {self.extraction_results['total_spare_parts']}

📁 **File Details:**
{''.join(summary_info)}

🔍 **Next Steps:**
1. Review extracted tables below
2. Download individual CSV files
3. Get combined spare parts CSV
"""
            
            # Create tables display
            tables_display = self._format_tables_display()
            
            # Create spare parts display
            spare_parts_display = self._format_spare_parts_display()
            
            self.processing_status = "Ready"
            return status_msg, tables_display, spare_parts_display
            
        except Exception as e:
            error_msg = f"❌ Error extracting tables: {str(e)}"
            logger.error(error_msg)
            self.processing_status = "Error"
            return error_msg, "", ""
    
    def _format_tables_display(self) -> str:
        """Format tables for display"""
        if not self.extraction_results:
            return "No tables extracted yet"
        
        display_parts = []
        
        for file_result in self.extraction_results["files"]:
            if file_result["total_tables"] > 0:
                display_parts.append(f"\n## 📄 {file_result['filename']}\n")
                
                for table in file_result["tables"]:
                    display_parts.append(f"""
### 📊 Table {table['table_id']} (Page {table['page_number']})

```csv
{table['csv_content'][:500]}{'...' if len(table['csv_content']) > 500 else ''}
```
""")
        
        return "\n".join(display_parts) if display_parts else "No tables found"
    
    def _format_spare_parts_display(self) -> str:
        """Format spare parts for display"""
        if not self.extraction_results:
            return "No spare parts extracted yet"
        
        all_spare_parts = []
        for file_result in self.extraction_results["files"]:
            all_spare_parts.extend(file_result.get("spare_parts", []))
        
        if not all_spare_parts:
            return "No spare parts found in the extracted tables"
        
        # Create a summary table
        try:
            df = pd.DataFrame(all_spare_parts)
            
            # Show first 10 rows as preview
            preview_df = df.head(10)
            
            display = f"""
## 🔧 Spare Parts Summary

**Total spare parts found: {len(all_spare_parts)}**

### Preview (First 10 items):

{preview_df.to_markdown(index=False) if not preview_df.empty else "No data to display"}

### Available Columns:
{', '.join(df.columns.tolist()) if not df.empty else "No columns"}
"""
            
            return display
            
        except Exception as e:
            return f"Error formatting spare parts: {str(e)}"
    
    def download_all_tables(self) -> Optional[str]:
        """Create a ZIP file with all extracted tables"""
        try:
            if not self.extraction_results:
                return None
            
            # Create a ZIP file in memory
            zip_buffer = BytesIO()
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                
                for file_result in self.extraction_results["files"]:
                    filename_base = Path(file_result['filename']).stem
                    
                    # Add individual tables
                    for table in file_result["tables"]:
                        table_filename = f"{filename_base}_table_{table['table_id']}.csv"
                        zip_file.writestr(table_filename, table['csv_content'])
                    
                    # Add spare parts if available
                    if file_result.get("spare_parts"):
                        spare_parts_filename = f"{filename_base}_spare_parts.csv"
                        df = pd.DataFrame(file_result["spare_parts"])
                        csv_content = df.to_csv(index=False)
                        zip_file.writestr(spare_parts_filename, csv_content)
                
                # Add combined spare parts file
                all_spare_parts = []
                for file_result in self.extraction_results["files"]:
                    all_spare_parts.extend(file_result.get("spare_parts", []))
                
                if all_spare_parts:
                    combined_df = pd.DataFrame(all_spare_parts)
                    combined_csv = combined_df.to_csv(index=False)
                    zip_file.writestr("all_spare_parts_combined.csv", combined_csv)
            
            # Save ZIP file
            zip_filename = "extracted_tables.zip"
            with open(zip_filename, 'wb') as f:
                f.write(zip_buffer.getvalue())
            
            logger.info(f"📦 Created ZIP file: {zip_filename}")
            return zip_filename
            
        except Exception as e:
            logger.error(f"❌ Error creating ZIP file: {str(e)}")
            return None
    
    def get_spare_parts_csv(self) -> Optional[str]:
        """Get combined spare parts as CSV file"""
        try:
            if not self.extraction_results:
                return None
            
            all_spare_parts = []
            for file_result in self.extraction_results["files"]:
                all_spare_parts.extend(file_result.get("spare_parts", []))
            
            if not all_spare_parts:
                return None
            
            # Create CSV file
            df = pd.DataFrame(all_spare_parts)
            csv_filename = "spare_parts_combined.csv"
            df.to_csv(csv_filename, index=False)
            
            logger.info(f"📊 Created spare parts CSV: {csv_filename}")
            return csv_filename
            
        except Exception as e:
            logger.error(f"❌ Error creating spare parts CSV: {str(e)}")
            return None
    
    def create_interface(self):
        """Create specialized Gradio interface for table extraction"""
        
        with gr.Blocks(title="Table Extractor - PDF to CSV", theme=gr.themes.Soft()) as interface:
            
            gr.Markdown("""
            # 📊 Advanced Table Extractor
            ## PDF → CSV Conversion with Spare Parts Detection
            
            Upload PDF documents to extract tables as CSV files. Specialized for spare parts catalogs and technical documentation.
            """)
            
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("### 📄 Upload & Process")
                    
                    file_upload = gr.File(
                        label="Upload PDF Files",
                        file_count="multiple",
                        file_types=[".pdf"]
                    )
                    
                    extract_btn = gr.Button("🔄 Extract Tables", variant="primary", size="lg")
                    
                    processing_status = gr.Textbox(
                        label="Processing Status",
                        lines=12,
                        interactive=False
                    )
                
                with gr.Column(scale=1):
                    gr.Markdown("### 📊 Extracted Tables")
                    
                    tables_display = gr.Markdown(
                        value="Upload and process PDF files to see extracted tables here.",
                        label="Tables Preview"
                    )
            
            with gr.Row():
                gr.Markdown("### 🔧 Spare Parts Summary")
            
            with gr.Row():
                spare_parts_display = gr.Markdown(
                    value="Spare parts will appear here after processing.",
                    label="Spare Parts"
                )
            
            with gr.Row():
                with gr.Column():
                    gr.Markdown("### 💾 Download Options")
                    
                    with gr.Row():
                        download_all_btn = gr.Button("📦 Download All Tables (ZIP)", variant="secondary")
                        download_spare_parts_btn = gr.Button("🔧 Download Spare Parts CSV", variant="secondary")
                    
                    download_status = gr.Textbox(
                        label="Download Status",
                        lines=2,
                        interactive=False
                    )
            
            # Event handlers
            extract_btn.click(
                fn=self.extract_tables_sync,
                inputs=[file_upload],
                outputs=[processing_status, tables_display, spare_parts_display]
            )
            
            download_all_btn.click(
                fn=self.download_all_tables,
                outputs=[gr.File(label="Download ZIP")]
            )
            
            download_spare_parts_btn.click(
                fn=self.get_spare_parts_csv,
                outputs=[gr.File(label="Download CSV")]
            )
        
        return interface

def main():
    """Main function to run the table extraction application"""
    try:
        # Create and launch app
        app = TableExtractionApp()
        interface = app.create_interface()
        
        # Launch with custom settings
        interface.launch(
            server_name="0.0.0.0",
            server_port=7862,
            share=False,
            debug=False,
            show_error=True
        )
        
    except Exception as e:
        logger.error(f"Failed to start table extraction application: {str(e)}")
        raise

if __name__ == "__main__":
    main()
